import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { PDFService } from "@/services/pdfService";
import {
  fetchObservationWithActions,
  ObservationResponse,
} from "@/services/api";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import ActionHistorySection from "./ActionHistorySection";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Observation,
  MyAction,
  Attachment,
  ObservationAction,
} from "@/types/observation";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { cn } from "@/lib/utils";
import ImageComponent from "@/components/common/ImageComponent";
import { fetchAllUsers, User } from "@/services/api";



interface ObservationDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  observation?: Observation | null;
  myAction?: MyAction | null;
  onReviewSubmit?: (observationId: string, reviewData: any) => void;
  currentUser?: string;
}

const ObservationDetailsModal: React.FC<ObservationDetailsModalProps> = ({
  open,
  onOpenChange,
  observation,
  myAction,
  onReviewSubmit,
  currentUser,
}) => {
  const data = observation || myAction;
  const { accessToken } = useSelector((state: RootState) => state.auth);

  // State for users
  const [users, setUsers] = useState<User[]>([]);



  // Fetch users when component mounts
  useEffect(() => {
    if (accessToken && open) {
      fetchAllUsers(accessToken)
        .then((data) => {
          setUsers(data);
        })
        .catch((error) => {
          console.error("Error fetching users:", error);
        });
    }
  }, [accessToken, open]);

  // Function to get user name by ID
  const getName = (id: string) => {
    if (!id) return "";
    const user = users.find((user) => user.id === id);
    return user ? user.firstName : id;
  };

  // Function to get multiple user names from assignedToId array
  const getAssignedNames = (assignedToId: string[] | undefined) => {
    if (!assignedToId || assignedToId.length === 0) return "";

    // Handle multiple assigned users
    const names = assignedToId.map(id => getName(id)).filter(name => name !== "");
    return names.join(", ");
  };

  // No longer need the review form

  // Fetch detailed observation data when modal opens
 

  if (!data) return null;

  const handleDownloadPDF = async () => {
    if (!observation) {
      console.error("No observation data available for PDF generation");
      return;
    }

    try {
      // Show loading state (you can add a loading indicator here)
      console.log("Generating PDF...");

      

      console.log("PDF generated successfully");
    } catch (error) {
      console.error("Error generating PDF:", error);
      // You can add a toast notification here to inform the user
    }
  };



  const getBadgeClass = (status: string) => {
    switch (status) {
      case "New":
        return "bg-safety-400 hover:bg-safety-500";
      case "Open":
        return "bg-warning-400 hover:bg-warning-500 text-black";
      case "In Progress":
        return "bg-primary hover:bg-primary/90";
      case "Action in Progress":
        return "bg-blue-600 hover:bg-blue-700 text-white";
      case "Rectified on Spot":
        return "bg-purple-500 hover:bg-purple-600 text-white";
      case "Pending Review":
        return "bg-muted-foreground hover:bg-gray-600 text-white";
      case "In Review":
        return "bg-orange-500 hover:bg-orange-600 text-white";
      case "Completed":
        return "bg-green-600 hover:bg-green-700 text-white";
      case "Closed":
        return "bg-success-500 hover:bg-success-600";
      case "Action Completed & Closed":
        return "bg-indigo-600 hover:bg-indigo-700 text-white";
      default:
        return "bg-muted hover:bg-muted/90";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>
            Observation Details - {"maskId" in data ? data.maskId : data.id}
          </DialogTitle>
          <DialogDescription>
            Complete information about this observation
          </DialogDescription>
        </DialogHeader>

        <div id="observation-details" className="p-4 space-y-6">
          {/* Header with logo placeholder */}
          <div className="flex justify-between items-center border-b pb-4">
            <div className="text-xl font-bold">Observation Report</div>
            <div className="text-sm text-muted-foreground">
              Generated: {format(new Date(), "PPP")}
            </div>
          </div>

          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium border-b pb-2">
              Basic Information
            </h3>
            <div className="grid grid-cols-2 gap-4">
              {/* Location Details */}
              <div className="col-span-2">
                <p className="text-sm text-muted-foreground">Location</p>
                <p className="font-medium">{data.location}</p>
              </div>

              {observation && (
                <>
                  <div>
                    <p className="text-sm text-muted-foreground">Category</p>
                    <p className="font-medium">{observation.category}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Type</p>
                    <p className="font-medium">{observation.type}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      Action/Condition
                    </p>
                    <p className="font-medium">{observation.actionCondition}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Status</p>
                    <div className="mt-1">
                      <Badge className={getBadgeClass(observation.status)}>
                        {observation.status}
                      </Badge>
                    </div>
                  </div>
                </>
              )}

              {!observation && data.dueDate && (
                <div>
                  <p className="text-sm text-muted-foreground">Due Date</p>
                  <p className="font-medium">
                    Due:{" "}
                    {typeof data.dueDate === "string"
                      ? data.dueDate
                      : format(data.dueDate, "PPP")}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <h3 className="text-lg font-medium border-b pb-2">Description</h3>
            <p>{data.description}</p>
          </div>

          {/* People Involved */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium border-b pb-2">
              People Involved
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Reported By</p>
                <p className="font-medium">
                  {observation
                    ? observation.reportedBy
                    : myAction && "submittedBy" in myAction
                    ? myAction.submittedBy
                    : "Unknown"}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Reported Date</p>
                <p className="font-medium">
                  {observation
                    ? format(observation.reportedDate, "PPP")
                    : "N/A"}
                </p>
              </div>
              {observation && observation.actionAssignee && (
                <div>
                  <p className="text-sm text-muted-foreground">
                    Action Assignee
                  </p>
                  <p className="font-medium">{observation.actionAssignee}</p>
                </div>
              )}
              {observation && observation.reviewedBy && (
                <div>
                  <p className="text-sm text-muted-foreground">Reviewed By</p>
                  <p className="font-medium">{observation.reviewedBy}</p>
                </div>
              )}
            </div>
          </div>

          {/* Images Section */}
          {observation &&
            observation.attachments &&
            observation.attachments.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium border-b pb-2">Images</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {observation.attachments.map(
                    (file: Attachment | string, fileIndex: number) => (
                      <ImageComponent
                        key={fileIndex}
                        fileName={
                          typeof file === "string" ? file : file.name
                        }
                        name={false}
                      />
                    )
                  )}
                </div>
              </div>
            )}
          {observation?.type === "Unsafe" && (
            <div className="p-4 border rounded-md bg-muted/30">
              {/* Debug: {JSON.stringify(observation.observationActions)} */}

              {observation.rectifiedOnSpot === false && (
                <>
                  {observation.observationActions &&
                    observation.observationActions.map((action, i) => {
                      // Use external counter logic similar to test.js
                      let k = 0;

                      if (action.actionType === 'take_action' || action.actionType === "reperform_action") {
                        // Find action owner object like in test.js (for future use)
                        // const actionOwnerObject = observation.observationActions.slice().reverse().find(item => item.actionType === "take_action");

                        // Calculate k based on filtered actions
                        k = observation.observationActions
                          .filter(a => a.actionType === "take_action" || a.actionType === "reperform_action")
                          .indexOf(action) + 1;

                        const isCompleted = action.status === "Completed";
                        const isInitiated = action.status === "Initiated";
                        const assignedName = getAssignedNames(action.assignedToId);
                        const formattedDate = action.createdDate
                          ? format(new Date(action.createdDate), "do MMM yyyy, hh:mm:ss a")
                          : "N/A";

                        return (
                          <div key={i} className="p-4 border rounded-md space-y-4 bg-muted/30 mb-4">
                            <div className="grid grid-cols-12 gap-4">
                              <div className="col-span-12">
                                <div className="grid grid-cols-12 gap-4">
                                  {k === 1 ? (
                                    <>
                                      <div className="col-span-6">
                                        <p className="font-semibold">Assigned Action {observation.maskId} - A{k}</p>
                                        <p className="text-sm text-muted-foreground">{action.actionToBeTaken}</p>
                                      </div>
                                      <div className="col-span-6">
                                        <p className="font-semibold">Due Date</p>
                                        <p className="text-sm text-muted-foreground">
                                          {action.dueDate
                                            ? (() => {
                                                try {
                                                  const date = new Date(action.dueDate);
                                                  return isNaN(date.getTime()) ? "-" : format(date, "do MMM yyyy");
                                                } catch {
                                                  return "-";
                                                }
                                              })()
                                            : "-"}
                                        </p>
                                      </div>
                                      <div className="col-span-6">
                                        {isInitiated && (
                                          <>
                                            <p className="font-semibold">Action Assignee</p>
                                            <p className="text-sm text-muted-foreground">{assignedName}</p>
                                          </>
                                        )}
                                      </div>
                                    </>
                                  ) : (
                                    <>
                                      <div className="col-span-12">
                                        <p className="font-semibold">Action Verifier Comments & Reassigned Action {observation.maskId} - A{k}</p>
                                        <p className="text-sm text-muted-foreground">{action.actionToBeTaken || 'No comments available'}</p>
                                      </div>
                                      <div className="col-span-12">
                                        {isInitiated && (
                                          <>
                                            <p className="font-semibold">Action Assignee</p>
                                            <p className="text-sm text-muted-foreground">{assignedName}</p>
                                          </>
                                        )}
                                      </div>
                                    </>
                                  )}
                                </div>
                              </div>
                            </div>

                            {isCompleted && (
                              <>
                                <div className="grid grid-cols-12 gap-4 mb-3">
                                  <div className="col-span-12">
                                    <p className="font-semibold">Action Taken</p>
                                    <p className="text-sm text-muted-foreground">{action.actionTaken}</p>
                                  </div>
                                </div>

                                <div className="grid grid-cols-12 gap-4 mb-3">
                                  <div className="col-span-6">
                                    <p className="font-semibold">Action Taken By</p>
                                    <p className="text-sm text-muted-foreground">{assignedName}</p>
                                  </div>
                                  <div className="col-span-6">
                                    <p className="font-semibold">Date</p>
                                    <p className="text-sm text-muted-foreground">{formattedDate}</p>
                                  </div>
                                </div>

                                <div className="col-span-12">
                                  {action.uploads && action.uploads.length > 0 && (
                                    <>
                                      <p className="font-semibold">Evidence</p>
                                      <div className="flex gap-2">
                                        {action.uploads.map((item, index) => (
                                          <div key={index} className="relative">
                                            <div className="shadow-md flex items-center">
                                              <ImageComponent
                                                fileName={item}
                                                size="100"
                                                name={true}
                                              />
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </>
                                  )}
                                </div>
                              </>
                            )}
                          </div>
                        );
                      } else if (action.actionType === 'verify_action') {
                        // Calculate k for verify action
                        k = observation.observationActions
                          .filter(a => a.actionType === "take_action" || a.actionType === "reperform_action")
                          .length;

                        const isCompleted = action.status === "Completed";
                        const isInitiated = action.status === "Initiated";
                        const assignedName = getAssignedNames(action.assignedToId);
                        const formattedDate = action.createdDate
                          ? format(new Date(action.createdDate), "do MMM yyyy, hh:mm:ss a")
                          : "N/A";

                        return (
                          <div key={i} className="p-4 border rounded-md space-y-4 bg-muted/30 mb-4">
                            {isInitiated ? (
                              <div className="grid grid-cols-12 gap-4 mb-3">
                                <div className="col-span-6">
                                  <p className="font-semibold">Action Verifier - A{k}</p>
                                  <p className="text-sm text-muted-foreground">{assignedName}</p>
                                </div>
                              </div>
                            ) : isCompleted ? (
                              <>
                                <div className="grid grid-cols-12 gap-4 mb-3">
                                  <div className="col-span-6">
                                    <p className="font-semibold">Action Verified By</p>
                                    <p className="text-sm text-muted-foreground">{observation.reviewedBy || 'N/A'}</p>
                                  </div>
                                  <div className="col-span-6">
                                    <p className="font-semibold">Date</p>
                                    <p className="text-sm text-muted-foreground">{formattedDate}</p>
                                  </div>
                                </div>
                              </>
                            ) : null}
                          </div>
                        );
                      } else if (action.actionType === 'review') {
                        const isInitiated = action.status === "Initiated";
                        const assignedName = getAssignedNames(action.assignedToId);
                        const formattedDate = action.createdDate
                          ? format(new Date(action.createdDate), "do MMM yyyy, hh:mm:ss a")
                          : "N/A";

                        return (
                          <div key={i} className="p-4 border rounded-md space-y-4 bg-muted/30 mb-4">
                            <div className="grid grid-cols-12 gap-4 mb-3">
                              <div className="col-span-6">
                                <p className="font-semibold">{isInitiated ? "Action Reviewer" : "Action Reviewed By"}</p>
                                <p className="text-sm text-muted-foreground">{assignedName}</p>
                              </div>
                              <div className="col-span-6">
                                <p className="font-semibold">Date</p>
                                <p className="text-sm text-muted-foreground">{formattedDate}</p>
                              </div>
                            </div>
                          </div>
                        );
                      }
                      return null;
                    })}
                </>
              )}

              {observation.rectifiedOnSpot && (
                <div className="p-4 border rounded-md bg-muted/30 space-y-4">
                  <div className="col-span-12">
                    <div className="grid grid-cols-12 gap-4 mb-3">
                      <div className="col-span-12">
                        <p className="font-semibold">Action Taken</p>
                        <p className="text-sm text-muted-foreground">{observation.actionTaken}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-12 gap-4">
                      <div className="col-span-12">
                        {observation.evidence && observation.evidence.length > 0 && (
                          <>
                            <p className="font-semibold">Evidence</p>
                            <div className="grid grid-cols-12 gap-2">
                              {observation.evidence.map((item, index) => (
                                <div key={index} className="col-span-3 relative">
                                  <div className="shadow-md flex items-center">
                                    <ImageComponent
                                      fileName={item}
                                      size="100"
                                      name={true}
                                    />
                                  </div>
                                </div>
                              ))}
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Debug Information */}

          {/* Action History Section */}

          {/* Footer */}
          <div className="border-t pt-4 text-sm text-muted-foreground">
            <p>
              This is an official safety observation record. Please maintain
              confidentiality.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>

          <Button onClick={handleDownloadPDF}>
            <Download className="h-4 w-4 mr-2" /> Download PDF
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ObservationDetailsModal;
