import React, { useEffect, useState } from 'react'
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { MultiSelect } from 'primereact/multiselect';
import { InputTextarea } from 'primereact/inputtextarea'
import { InputText } from 'primereact/inputtext'
import { Checkbox } from 'primereact/checkbox';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import 'primeicons/primeicons.css';
import { DropzoneArea } from 'material-ui-dropzone';
import { TabView, TabPanel } from 'primereact/tabview';
import { Stepper, Step, StepLabel, Typography } from '@mui/material';
import { Calendar } from 'primereact/calendar';
import Accordion from 'react-bootstrap/Accordion';
import { RadioButton } from 'primereact/radiobutton';
import Select from 'react-select'
import Swal from 'sweetalert2';
// import { useLocation, useHistory } from 'react-router-dom/cjs/react-router-dom';
import moment from 'moment';
import { Modal } from 'react-bootstrap';

// Mock constants and API for testing
const GMS1_URL = '/api/gms1';
const GET_USER_ROLE_BY_MODE = '/api/user-role';
const HAZARDS_CATEGOTY = '/api/hazards-category';
const RISK_UPDATE_WITH_ID_URL = (id) => `/api/risk-update/${id}`;
const GET_ALL_USER = '/api/users';
const FILE_URL = '/api/file-upload';
const RISKASSESSMENT_LIST = '/api/risk-assessment';
const GET_RISK_HAZARD_URL = '/api/risk-hazard';

// Mock API object
const API = {
    get: async (url) => ({ status: 200, data: [] }),
    post: async (url, data) => ({ status: 200, data: { files: [{ originalname: 'test.jpg' }] } })
};

// Mock Redux selector
const useSelector = (selector) => ({
    id: 1,
    firstName: 'Test User'
});

// FullLoader Component
const FullLoader = () => (
    <div className="d-flex justify-content-center align-items-center" style={{ height: '200px' }}>
        <div className="spinner-border" role="status">
            <span className="sr-only">Loading...</span>
        </div>
    </div>
);

// ImageComponent Mock
const ImageComponent = ({ fileName, size, name }) => (
    <div className="d-flex align-items-center justify-content-center" style={{ width: size, height: size, border: '1px solid #ccc' }}>
        {name && <span>{fileName}</span>}
    </div>
);

// SubActivityComponent
const SubActivityComponent = ({ item, onSave }) => {
    const [isEditing, setIsEditing] = useState(false);
    const [activityName, setActivityName] = useState(item[0].name);
    const [uploadedImages, setUploadedImages] = useState(item[0].images || []);

    const toggleEditMode = () => {
        setIsEditing(!isEditing);
    };

    const saveChanges = () => {
        onSave(activityName, uploadedImages);
        setIsEditing(false);
    };

    const cancelChanges = () => {
        setActivityName(item[0].name);
        setUploadedImages(item[0].images);
        setIsEditing(false);
    };

    return (
        <div className='p-4 mb-4 col-12'>
            <div className='col-12'>
                <div className="d-flex align-items-center col-12">
                    {!isEditing ? (
                        <>
                            <h4 className='me-2'>{activityName}</h4>
                            <i
                                className="pi pi-pencil"
                                style={{ cursor: 'pointer' }}
                                onClick={toggleEditMode}
                            />
                        </>
                    ) : (
                        <div className="d-flex flex-column col-12">
                            <label htmlFor="username" className='mb-2'>Sub-Activity Name</label>
                            <InputText
                                value={activityName}
                                onChange={(e) => setActivityName(e.target.value)}
                            />
                            <div className="mt-3">
                                <Button label="Save" onClick={saveChanges} className="me-2" />
                                <Button label="Cancel" onClick={cancelChanges} outlined />
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

// HazardPanel Component
const HazardPanel = ({ hazards, activeTabIndex, setActiveTabIndex, selectedHazards, onClickHazards }) => {
    return (
        <div className="w-100">
            <TabView activeIndex={activeTabIndex} onTabChange={(e) => setActiveTabIndex(e.index)}>
                {hazards.map((hazard, index) => (
                    <TabPanel key={index} header={hazard.name}>
                        <div className="row">
                            {hazard.hazardItems?.map((item, j) => (
                                <div key={j} className="col-4 mb-3">
                                    <div
                                        className={`p-3 text-center border rounded cursor-pointer ${
                                            selectedHazards.some(h => h.id === item.id) ? 'bg-primary text-white' : 'bg-light'
                                        }`}
                                        onClick={() => onClickHazards(item, j)}
                                        style={{ cursor: 'pointer' }}
                                    >
                                        <p className="mb-0">{item.name}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </TabPanel>
                ))}
            </TabView>
        </div>
    );
};

// HazardAccordion Component
const HazardAccordion = ({ hazards, activeTabIndex, setActiveTabIndex, selectedHazards, onClickHazards, required, item }) => {
    return (
        <Accordion defaultActiveKey={'0'}>
            <Accordion.Item eventKey="0">
                <Accordion.Header style={(required === false && item[1].selected.length === 0) ? { border: '1px solid red' } : {}}>
                    <div>
                        <h6 className='fw-bold'>Hazards Identification</h6>
                        <p>Identify potential hazards associated with sub-activity</p>
                    </div>
                </Accordion.Header>
                <Accordion.Body>
                    <div className="d-flex" style={{ border: '1px solid #E5E7EB' }}>
                        <HazardPanel
                            hazards={hazards}
                            activeTabIndex={activeTabIndex}
                            setActiveTabIndex={setActiveTabIndex}
                            selectedHazards={item[1].selected}
                            onClickHazards={onClickHazards}
                        />
                    </div>
                </Accordion.Body>
            </Accordion.Item>
        </Accordion>
    );
};

// IdentifiedHazards Component
const IdentifiedHazards = ({ selectedHazards, onDeleteHaz }) => {
    return (
        <>
            <h6 className='mt-4 mb-3'>Hazards Identified</h6>
            <div className='row'>
                {selectedHazards.map((item, index) => (
                    <div className='col-3 mb-3' key={index}>
                        <div className='d-flex justify-content-between align-items-center p-2' style={{ border: '1px solid #E5E7EB', borderRadius: 8 }}>
                            {!localStorage.getItem('SELECTED_INDUSTRIES') && (
                                <img
                                    src={`https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/${item.image}`}
                                    style={{ height: 40 }}
                                    alt="sample"
                                />
                            )}
                            <p>{item.name}</p>
                            <i className='pi pi-times' onClick={() => onDeleteHaz(item)}></i>
                        </div>
                    </div>
                ))}
            </div>
        </>
    );
};

// Consequence Component
const Consequence = ({ con, i, impactOn, onImapactOn, onConseqText, onDeleteConseq, handleTaskFileChange, required, type, handleRemoveImage }) => {
    return (
        <>
            <div className="row mt-4 ">
                <div className="col-3">
                    <p>Impact on</p>
                    <Select
                        options={impactOn}
                        value={impactOn.find(option => option.value === con.current_type)}
                        onChange={(e) => onImapactOn(e.value, i, 'consequence')}
                        className={`d-flex ${(required === false && con.current_type === '') ? 'borderRed' : ''}`}
                        styles={{
                            container: (provided) => ({
                                ...provided,
                                width: '100%'
                            }),
                            control: (provided) => ({
                                ...provided,
                                width: '100%'
                            })
                        }}
                    />
                </div>
                <div className="col-8">
                    <p>Description</p>
                    <InputTextarea
                        style={{ width: '100%' }}
                        value={con.value}
                        autoResize
                        onChange={(e) => onConseqText(e.target.value, i, 'consequence')}
                        className={`${(required === false && con.value === '') ? 'borderRed' : ''}`}
                    />
                </div>
                <div className="col-1 text-center">
                    <i className="pi pi-trash mb-3" onClick={() => onDeleteConseq(i, 'consequence')}></i>
                </div>
            </div>

            <div className="col-12 mt-3">
                <label htmlFor="imageUploads" className="mb-2">
                    {type === 'hazard' ? 'Where possible and available, upload images to illustrate these points. These images will be used to visually communicate the risks and their potential consequences to relevant personnel.' : 'Upload relevant images to visually represent the identified consequences for this sub-activity. These images may also be utilized in other applicable modules to support effective risk communication'}
                </label>
                <div className="mb-3">
                    <DropzoneArea
                        acceptedFiles={['image/jpeg', 'image/png']}
                        dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                        filesLimit={5}
                        maxFileSize={104857600}
                        onChange={(files) => handleTaskFileChange(files, i, 'consequence')}
                        showPreviewsInDropzone={false}
                        showPreviews={false}
                        dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center '}
                    />
                </div>
            </div>

            <div className="col-12 mt-3">
                <label htmlFor="uploaded" className="mb-2">Uploaded</label>
            </div>
            <div className="col-12 mt-3 mb-3">
                <div className="row">
                    {con.files && con.files.map((item, m) => (
                        <div key={m} className="col-3  " style={{ position: 'relative' }}>
                            <div className="boxShadow d-flex align-items-center justify-content-center" >
                                <ImageComponent fileName={item} size={'100'} name={true}/>
                                <i
                                className="pi pi-trash"
                                onClick={() => handleRemoveImage(m,i,'consequence')}
                                style={{
                                    position: 'absolute',
                                    top: '5px',
                                    right: '5px',
                                    cursor: 'pointer',
                                    color: 'red',
                                }}
                            />
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </>
    );
};

// CurrentControl Component
const CurrentControl = ({ con, i, control, controlType, onImapactOn, onConseqText, onDeleteConseq, onConseqRequired, handleTaskFileChange, onMethodOn, required, type, handleRemoveMainImage }) => {
    return (
        <>
            <div className="row mt-4">
                <div className="col-3">
                    <p>Method of Control</p>
                    <Select
                        options={control}
                        value={control.find(option => option.value === con.current_type)}
                        onChange={(e) => onImapactOn(e.value, i, 'current_control')}
                        className={`d-flex ${(required === false && con.current_type === '') ? 'borderRed' : ''}`}
                        styles={{
                            container: (provided) => ({
                                ...provided,
                                width: '100%'
                            }),
                            control: (provided) => ({
                                ...provided,
                                width: '100%'
                            })
                        }}
                    />
                </div>
                {con.current_type !== "No Control" && (<>
                    <div className="col-3">
                        <p>Purpose of Control</p>
                        <Select
                            options={controlType}
                            value={controlType.find(option => option.value === con.method)}
                            onChange={(e) => onMethodOn(e.value, i, 'current_control')}
                            className={`d-flex ${(required === false && con.current_type === '') ? 'borderRed' : ''}`}
                            styles={{
                                container: (provided) => ({
                                    ...provided,
                                    width: '100%'
                                }),
                                control: (provided) => ({
                                    ...provided,
                                    width: '100%'
                                })
                            }}
                        />
                    </div>
                    <div className="col-5">
                        <p>Provide a brief description of the control that is implemented</p>
                        <InputTextarea
                            style={{ width: '100%' }}
                            value={con.value}
                            onChange={(e) => onConseqText(e.target.value, i, 'current_control')}
                            className={`${(required === false && con.value === '') ? 'borderRed' : ''}`}
                            autoResize
                        />
                    </div>
                    <div className="col-1 text-center">
                        <i className="pi pi-trash mb-3" onClick={() => onDeleteConseq(i, 'current_control')}></i>
                    </div>
                </>)}
            </div>
            <div className='row mt-4 mb-4'>
                <div className="d-flex flex-wrap justify-content-end gap-3">
                    <div className="d-flex align-items-center">
                        <Checkbox
                            inputId="ingredient1"
                            name="pizza"
                            value="required"
                            onChange={() => onConseqRequired(con.required, i, 'current_control', 'required')}
                            checked={con.required}
                            style={{ marginRight: '8px' }}
                        />
                        <label htmlFor="ingredient1" style={{ marginLeft: '8px' }}>
                            Required
                        </label>
                    </div>
                    <div className="d-flex align-items-center">
                        <Checkbox
                            inputId="ingredient2"
                            name="pizza"
                            value="validity"
                            onChange={() => onConseqRequired(con.validity, i, 'current_control', 'validity')}
                            checked={con.validity}
                            style={{ marginRight: '8px' }}
                        />
                        <label htmlFor="ingredient2" style={{ marginLeft: '8px' }}>
                            Validity
                        </label>
                    </div>
                </div>
            </div>
            {con.current_type !== "No Control" && (<>
                <div className="col-12 mt-3">
                    <label htmlFor="imageUploads" className="mb-2">
                        {type === 'hazard' ? 'Where applicable, upload images that illustrate the controls to be implemented for this Critical High Risk Activity. These images will help enhance understanding and communication of the controls to personnel involved in managing the associated risks' : 'Upload relevant images to visually represent the current controls identified here. These images will be used in other modules as appropriate.'}
                    </label>
                    <div className="mb-3">
                        <DropzoneArea
                            acceptedFiles={['image/jpeg', 'image/png']}
                            dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                            filesLimit={5}
                            maxFileSize={104857600}
                            onChange={(files) => handleTaskFileChange(files, i, 'current_control')}
                            showPreviewsInDropzone={false}
                            showPreviews={false}
                            dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center '}
                        />
                    </div>
                </div>
                <div className="col-12 mt-3">
                    <label htmlFor="uploaded" className="mb-2">Uploaded</label>
                </div>
                <div className="col-12 mt-3 mb-3">
                    <div className="row">
                        {con.files && con.files.map((item, m) => (
                            <div key={m} className="col-3" style={{ position: 'relative' }}>
                                <div className="boxShadow d-flex align-items-center justify-content-center">
                                    <ImageComponent fileName={item} size={'100'} name={true} />
                                    <i
                                        className="pi pi-trash"
                                        onClick={() => handleRemoveMainImage(m, i, 'current_control')}
                                        style={{
                                            position: 'absolute',
                                            top: '5px',
                                            right: '5px',
                                            cursor: 'pointer',
                                            color: 'red',
                                        }}
                                    />
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </>)}
        </>
    );
};

// Severity Component
const Severity = ({ severity, severityData, required, onChangeSeverity, item }) => {
    const [severityTable, setSeverityTable] = useState(false);

    const severityOptions = severity.map(option => ({
        value: option.value || option,
        label: option.label || option
    }));

    return (
        <div className="row mt-4 mb-3 pb-4" style={{ borderBottom: '1px solid #E0E0E0' }}>
            <div className='col-8'>
                <h6 className='fw-bold'>Severity</h6>
                <p className='fst-italic'>Degree of harm or impact that could result from a hazardous event or situation</p>
            </div>
            <div className='col-4'>
                <Select
                    className={`d-flex ${(required === false && item[4].severity === '') ? 'borderRed' : ''}`}
                    options={severityOptions}
                    value={severityOptions.find(option => option.value === item[4].severity)}
                    onChange={(e) => onChangeSeverity(e, 'assessment')}
                    styles={{
                        container: (provided) => ({ ...provided, width: '100%' }),
                        control: (provided) => ({ ...provided, width: '100%' })
                    }}
                />
            </div>
            <h6 className='mt-3 pointer' onClick={() => setSeverityTable(!severityTable)}>
                Understand Severity Levels <i className={`pi ${severityTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i>
            </h6>
            {severityTable && (
                <div className='col-12 mt-3'>
                    <div className="card">
                        <DataTable value={severityData} className="table-bordered">
                            <Column field="id" header="Severity Level"></Column>
                            <Column field="severity" header="Descriptor"></Column>
                            <Column field="personnel" header="Personnel"></Column>
                            <Column field="property" header="Equipment / Property"></Column>
                            <Column field="environment" header="Environment"></Column>
                            <Column field="serviceLoss" header="Service Loss"></Column>
                        </DataTable>
                    </div>
                </div>
            )}
        </div>
    );
};

// Likelihood Component
const Likelihood = ({ likelyhood, levelData, required, onChangeLikelyhood, item, rowClassName }) => {
    const [likelyhoodTable, setLikelyhoodTable] = useState(false);

    const likelihoodOptions = likelyhood.map(option => ({
        value: option.value || option,
        label: option.label || option
    }));

    return (
        <div className="row mt-4 mb-3 pb-4" style={{ borderBottom: '1px solid #E0E0E0' }}>
            <div className='col-8'>
                <h6 className='fw-bold'>Likelihood</h6>
                <p className='fst-italic'>Probability or chance that a hazardous event will occur</p>
            </div>
            <div className='col-4'>
                <Select
                    className={`d-flex ${(required === false && item[4].likelyhood === '') ? 'borderRed' : ''}`}
                    options={likelihoodOptions}
                    value={likelihoodOptions.find(option => option.value === item[4].likelyhood)}
                    onChange={(e) => onChangeLikelyhood(e, 'assessment')}
                    styles={{
                        container: (provided) => ({ ...provided, width: '100%' }),
                        control: (provided) => ({ ...provided, width: '100%' })
                    }}
                />
            </div>
            <h6 className='mt-3 pointer' onClick={() => setLikelyhoodTable(!likelyhoodTable)}>
                Understand Likelihood Levels <i className={`pi ${likelyhoodTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i>
            </h6>
            {likelyhoodTable && (
                <div className='col-12 mt-3'>
                    <div className="card">
                        <DataTable value={levelData} className="table-bordered" rowClassName={rowClassName}>
                            <Column field="level" header="Level"></Column>
                            <Column field="descriptor" header="Descriptor"></Column>
                            <Column field="detailedDescription" header="Detailed Description"></Column>
                        </DataTable>
                    </div>
                </div>
            )}
        </div>
    );
};

// RiskLevel Component
const RiskLevel = ({ item, tableData, cellClassName, cellStyle }) => {
    const [riskTable, setRiskTable] = useState(false);

    return (
        <div className="row mt-4 mb-3 pb-4" style={{ borderBottom: '1px solid #E0E0E0' }}>
            <div className='col-8'>
                <h6 className='fw-bold'>Risk Level</h6>
                <p className='fst-italic'>Overall level of risk based on severity and likelihood assessment</p>
            </div>
            <div className='col-4'>
                <div className="p-2 border rounded bg-light">
                    <strong>Risk Level: {item[4].severity && item[4].likelyhood ?
                        (parseInt(item[4].severity) * parseInt(item[4].likelyhood)) : 'Not Calculated'}</strong>
                </div>
            </div>
            <h6 className='mt-3 pointer' onClick={() => setRiskTable(!riskTable)}>
                Risk Matrix <i className={`pi ${riskTable ? 'pi-angle-up' : 'pi-angle-down'} ms-1`}></i>
            </h6>
            {riskTable && (
                <div className='col-12 mt-3'>
                    <div className="card">
                        <DataTable value={tableData} className="table-bordered">
                            <Column field="severity" header="Severity"></Column>
                            <Column field="rare" header="Rare (1)" body={(data) =>
                                <span className={cellStyle(data, 'rare')}>{data.rare}</span>}></Column>
                            <Column field="unlikely" header="Unlikely (2)" body={(data) =>
                                <span className={cellStyle(data, 'unlikely')}>{data.unlikely}</span>}></Column>
                            <Column field="possible" header="Possible (3)" body={(data) =>
                                <span className={cellStyle(data, 'possible')}>{data.possible}</span>}></Column>
                            <Column field="likely" header="Likely (4)" body={(data) =>
                                <span className={cellStyle(data, 'likely')}>{data.likely}</span>}></Column>
                            <Column field="almostCertain" header="Almost Certain (5)" body={(data) =>
                                <span className={cellStyle(data, 'almostCertain')}>{data.almostCertain}</span>}></Column>
                        </DataTable>
                    </div>
                </div>
            )}
        </div>
    );
};

// AcceptableRisk Component
const AcceptableRisk = ({ item, onChangeReAss }) => {
    return (
        <div className="row mt-4 mb-3">
            <div className='col-8'>
                <h6 className='fw-bold'>Is this risk acceptable?</h6>
                <p className='fst-italic'>Determine if the current risk level is acceptable or requires additional controls</p>
            </div>
            <div className='col-4'>
                <div className="d-flex align-items-center">
                    <RadioButton
                        inputId="acceptable_yes"
                        name="acceptable"
                        value={true}
                        onChange={(e) => onChangeReAss(e.value)}
                        checked={item[5].accept === true}
                    />
                    <label htmlFor="acceptable_yes" className="ml-2 me-3">Yes</label>

                    <RadioButton
                        inputId="acceptable_no"
                        name="acceptable"
                        value={false}
                        onChange={(e) => onChangeReAss(e.value)}
                        checked={item[5].accept === false}
                    />
                    <label htmlFor="acceptable_no" className="ml-2">No</label>
                </div>
            </div>
        </div>
    );
};

// RiskAssessment Component
const RiskAssessment = ({
    item,
    severity,
    severityData,
    required,
    onChangeSeverity,
    likelyhood,
    levelData,
    onChangeLikelyhood,
    rowClassName,
    tableData,
    cellClassName,
    cellStyle,
    onChangeReAss
}) => {
    return (
        <div>
            <h6 className='fw-bold'>Estimate the Risk of this Sub Activity</h6>
            <p>For this Sub Activity, assess the Severity & Likelihood of the <b>identified consequences</b>, considering the <b>current controls</b> in place</p>
            <ul>
                <li>Use the tables below as a guide</li>
                <li>Assess how well the preventative controls reduce the chances of an event occurring and how mitigative controls limit the impact if an event does occur.</li>
                <li>Ensure that your assessment reflects the worst-case scenario, based on existing controls and for each impacted category (People, Environment etc.)</li>
            </ul>
            <p>This assessment will be used to determine the Risk Level for this sub-activity</p>

            <Severity
                severity={severity}
                severityData={severityData}
                required={required}
                onChangeSeverity={onChangeSeverity}
                item={item}
            />

            <Likelihood
                likelyhood={likelyhood}
                levelData={levelData}
                required={required}
                onChangeLikelyhood={onChangeLikelyhood}
                item={item}
                rowClassName={rowClassName}
            />

            <RiskLevel
                item={item}
                tableData={tableData}
                cellClassName={cellClassName}
                cellStyle={cellStyle}
            />

            <AcceptableRisk
                item={item}
                onChangeReAss={onChangeReAss}
            />
        </div>
    );
};

// ProposedRiskManagement Component
const ProposedRiskManagement = ({ item, controlAdditional, controlType, onControlAddion, onControlAddionText, onResponsePerson, onResponseDate, responsibility, addAdditionalControl, onDeleteConseq, required }) => {
    return (
        <div>
            <h6 className='fw-bold'>Additional Risk Management</h6>
            <p>Identify additional controls that could be implemented to further reduce the risk</p>

            {item[6].option.map((control, i) => (
                <div key={i} className="row mt-4 mb-4 p-3 border rounded">
                    <div className="col-3">
                        <p>Control Type</p>
                        <Select
                            options={controlAdditional}
                            value={controlAdditional.find(option => option.value === control.current_type)}
                            onChange={(e) => onControlAddion(e.value, i)}
                            className={`d-flex ${(required === false && control.current_type === '') ? 'borderRed' : ''}`}
                        />
                    </div>
                    <div className="col-6">
                        <p>Description</p>
                        <InputTextarea
                            style={{ width: '100%' }}
                            value={control.value}
                            onChange={(e) => onControlAddionText(e.target.value, i)}
                            className={`${(required === false && control.value === '') ? 'borderRed' : ''}`}
                            autoResize
                        />
                    </div>
                    <div className="col-2">
                        <p>Responsible Person</p>
                        <Select
                            options={responsibility.map(r => ({ value: r.id, label: r.name }))}
                            value={responsibility.find(r => r.id === control.person) ? { value: control.person, label: responsibility.find(r => r.id === control.person).name } : null}
                            onChange={(e) => onResponsePerson(e.value, i)}
                        />
                    </div>
                    <div className="col-1 text-center">
                        <i className="pi pi-trash mb-3" onClick={() => onDeleteConseq(i, 'responsibility')}></i>
                    </div>
                    <div className="col-12 mt-3">
                        <p>Target Date</p>
                        <Calendar
                            value={control.date}
                            onChange={(e) => onResponseDate(e.value, i)}
                            showIcon
                        />
                    </div>
                </div>
            ))}

            <Button
                label="Add Additional Control"
                onClick={addAdditionalControl}
                className="mt-3"
                outlined
            />
        </div>
    );
};

// TaskItem Component
const TaskItem = ({ task, index, openDialog, deleteTask, subActivity, handleDragStart, handleDrop, handleDragOver, draggedItemIndex, getStatusClass }) => {
    const getActivityName = (item) => {
        const activityItem = item.find(i => i.type === 'activity');
        return activityItem ? activityItem.name : 'Unnamed Activity';
    };

    return (
        <div
            className="col-12 mb-3"
            draggable
            onDragStart={(e) => handleDragStart(e, index)}
            onDrop={(e) => handleDrop(e, index)}
            onDragOver={handleDragOver}
            style={{
                opacity: draggedItemIndex === index ? 0.5 : 1,
                cursor: 'move'
            }}
        >
            <div className="card p-3">
                <div className="d-flex justify-content-between align-items-center">
                    <div className="d-flex align-items-center">
                        <i className="pi pi-bars me-2" style={{ cursor: 'grab' }}></i>
                        <h6 className="mb-0">{getActivityName(task)}</h6>
                    </div>
                    <div className="d-flex align-items-center">
                        <span className={`badge me-2 ${getStatusClass(task)}`}>
                            {task[11] && task[11].value ?
                                Object.values(task[11].value).some(status => status === 'completed') ? 'Completed' :
                                Object.values(task[11].value).some(status => status === 'inprogress') ? 'In Progress' : 'Not Started'
                                : 'Not Started'
                            }
                        </span>
                        <Button
                            icon="pi pi-pencil"
                            className="p-button-sm me-2"
                            onClick={() => openDialog(task, index)}
                        />
                        <Button
                            icon="pi pi-cog"
                            className="p-button-sm me-2"
                            onClick={() => subActivity(task, index)}
                        />
                        <Button
                            icon="pi pi-trash"
                            className="p-button-sm p-button-danger"
                            onClick={(e) => deleteTask(e, index)}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

// HeadStepper Component
const HeadStepper = ({ activeStage, stages, stageStatus, handleStageClick, getStatusClass }) => {
    return (
        <div className="stepper-container">
            <Stepper activeStep={activeStage} alternativeLabel>
                {stages.map((stage, index) => (
                    <Step key={index} onClick={() => handleStageClick(index)}>
                        <StepLabel>
                            <Typography variant="caption" className={getStatusClass(stageStatus)}>
                                {stage}
                            </Typography>
                        </StepLabel>
                    </Step>
                ))}
            </Stepper>
        </div>
    );
};

// RiskUpdate Component
const RiskUpdate = ({ updates, onSubmitUpdate }) => {
    return (
        <div>
            <h6 className='fw-bold'>Risk Updates</h6>
            <div className="mt-3">
                {updates.map((update, index) => (
                    <div key={index} className="card p-3 mb-3">
                        <div className="d-flex justify-content-between">
                            <div>
                                <p><strong>Date:</strong> {moment(update.createdAt).format('DD/MM/YYYY')}</p>
                                <p><strong>Update:</strong> {update.description}</p>
                            </div>
                            <div>
                                <p><strong>Status:</strong> {update.status}</p>
                            </div>
                        </div>
                    </div>
                ))}
                <Button label="Submit Update" onClick={onSubmitUpdate} />
            </div>
        </div>
    );
};

// UpdateTable Component
const UpdateTable = ({ updates }) => {
    return (
        <div>
            <h6 className='fw-bold'>Update History</h6>
            <DataTable value={updates} className="mt-3">
                <Column field="createdAt" header="Date" body={(rowData) => moment(rowData.createdAt).format('DD/MM/YYYY')}></Column>
                <Column field="description" header="Description"></Column>
                <Column field="status" header="Status"></Column>
                <Column field="updatedBy" header="Updated By"></Column>
            </DataTable>
        </div>
    );
};

// Main Routine Component
function Routine({ data = {}, type = 'routine', domain = 'create' }) {
    const user = useSelector((state) => state.login.user);

    // State variables
    const [files, setFiles] = useState([]);
    const [depart, setDepart] = useState([]);
    const [activity, setActivity] = useState([]);
    const [crew, setCrew] = useState([]);
    const [selectedDepart, setSelectedDepart] = useState(null);
    const [selectedActivity, setSelectedActivity] = useState(null);
    const [selectedCrew, setSelectedCrew] = useState([]);
    const [addSubActivity, setAddSubActivity] = useState(false);
    const [activityDesc, setActivityDesc] = useState('');
    const [task, setTask] = useState([]);
    const [subActivityName, setSubActivityName] = useState('');
    const [visible, setVisible] = useState(false);
    const [item, setItem] = useState('');
    const [index, setIndex] = useState('');
    const [hazards, setHazards] = useState([]);
    const [activeTabIndex, setActiveTabIndex] = useState(0);
    const [responsibility, setResponsibility] = useState([]);
    const [required, setRequired] = useState(true);
    const [isLoading, setIsLoading] = useState(false);
    const [Update, setUpdate] = useState([]);
    const [draggedItemIndex, setDraggedItemIndex] = useState(null);
    const [riskRoutine, setRiskRoutine] = useState([]);
    const [onEdit, setOnEdit] = useState(false);

    // Static data
    const severity = [
        { "value": "1", "label": "1(E) - Negligible" },
        { "value": "2", "label": "2(D) - Minor" },
        { "value": "3", "label": "3(C) - Moderate" },
        { "value": "4", "label": "4(B) - Major" },
        { "value": "5", "label": "5(A) - Catastrophic" }
    ];

    const likelyhood = [
        { label: "Rare (1)", value: "1" },
        { label: "Unlikely (2)", value: "2" },
        { label: "Possible (3)", value: "3" },
        { label: "Likely (4)", value: "4" },
        { label: "Almost Certain (5)", value: "5" },
    ];

    const impactOn = [
        { 'label': 'Personnel', 'value': 'Personnel' },
        { 'label': 'Environment', 'value': 'Environment' },
        { 'label': 'Property / Equipment', 'value': 'Property / Equipment' },
        { 'label': 'Operations', 'value': 'Operations' },
    ];

    const control = [
        { 'label': 'No Control', 'value': 'No Control' },
        { 'label': 'Engineering', 'value': 'Engineering' },
        { 'label': 'Administrative', 'value': 'Administrative' },
        { 'label': 'PPE', 'value': 'PPE' }
    ];

    const controlAdditional = [
        { 'label': 'Elimination', 'value': 'Elimination' },
        { 'label': 'Substitution', 'value': 'Substitution' },
        { 'label': 'Engineering', 'value': 'Engineering' },
        { 'label': 'Administrative', 'value': 'Administrative' },
        { 'label': 'PPE', 'value': 'PPE' }
    ];

    const controlType = [
        { 'label': 'Preventative', 'value': 'Preventative' },
        { 'label': 'Mitigative', 'value': 'Mitigative' }
    ];

    const severityData = [
        {
            id: '5 (A)',
            severity: 'Catastrophic',
            personnel: 'Serious injury with long-term or permanent disability or death.',
            property: 'Significant damage leading to major repairs.',
            environment: 'Significant environmental damage requiring regulatory reporting and cleanup.',
            serviceLoss: 'Major disruption to service operations, extended recovery time.'
        },
        {
            id: '4 (B)',
            severity: 'Major',
            personnel: 'Serious injury with long-term recovery or permanent disability.',
            property: 'Noticeable damage requiring repair, causing moderate downtime or repair costs.',
            environment: 'Moderate, recoverable environmental impact, requiring external agency notification or cleanup.',
            serviceLoss: 'Significant downtime with substantial recovery efforts.'
        },
        {
            id: '3 (C)',
            severity: 'Moderate',
            personnel: 'Injury requiring medical treatment with potential for short-term lost workdays or restricted duties.',
            property: 'Noticeable damage requiring repair, causing moderate downtime or repair costs.',
            environment: 'Moderate environmental impact, manageable on-site with potential regulatory notification.',
            serviceLoss: 'Moderate service interruption with short recovery.'
        },
        {
            id: '2 (D)',
            severity: 'Minor',
            personnel: 'Minor injury requiring first-aid or outpatient treatment, with minimal lost time.',
            property: 'Slight damage requiring minor repairs without significant downtime.',
            environment: 'Small localized impact, manageable on-site, without long-term environmental damage.',
            serviceLoss: 'Brief disruption to services, easily restored.'
        },
        {
            id: '1 (E)',
            severity: 'Insignificant',
            personnel: 'Minor first-aid required with no lost time or long-term health impacts.',
            property: 'Minimal damage or wear that does not require repair or interruption to operations.',
            environment: 'Negligible environmental impact with no regulatory involvement needed.',
            serviceLoss: 'No impact on services.'
        }
    ];

    const levelData = [
        {
            level: '1',
            descriptor: 'Rare',
            detailedDescription: 'The event is highly unlikely to occur under normal circumstances, with little to no historical precedent.'
        },
        {
            level: '2',
            descriptor: 'Unlikely',
            detailedDescription: 'The event is improbable but could potentially happen under unusual conditions, though there is limited historical data to support this.'
        },
        {
            level: '3',
            descriptor: 'Possible',
            detailedDescription: 'The event could happen, with moderate chances of occurring based on historical records or foreseeable conditions.'
        },
        {
            level: '4',
            descriptor: 'Likely',
            detailedDescription: 'The event is expected to occur in the normal course of operations, with a significant history of similar incidents.'
        },
        {
            level: '5',
            descriptor: 'Almost Certain',
            detailedDescription: 'The event is highly likely to occur and is anticipated in the near future without further intervention.'
        }
    ];

    const tableData = [
        { id: '5(A)', severity: 'Catastrophic', rare: '5(A)', unlikely: '10(A)', possible: '15(A)', likely: '20(A)', almostCertain: '25(A)' },
        { id: '4(B)', severity: 'Major', rare: '4(B)', unlikely: '8(B)', possible: '12(B)', likely: '16(B)', almostCertain: '20(B)' },
        { id: '3(C)', severity: 'Moderate', rare: '3(C)', unlikely: '6(C)', possible: '9(C)', likely: '12(C)', almostCertain: '15(C)' },
        { id: '2(D)', severity: 'Minor', rare: '2(D)', unlikely: '4(D)', possible: '6(D)', likely: '8(D)', almostCertain: '10(D)' },
        { id: '1(E)', severity: 'Insignificant', rare: '1(E)', unlikely: '2(E)', possible: '3(E)', likely: '4(E)', almostCertain: '5(E)' },
    ];

    // Utility functions
    const getStatusClass = (task) => {
        if (!task || !task[11] || !task[11].value) return 'bg-secondary';

        const statuses = Object.values(task[11].value);
        if (statuses.some(status => status === 'completed')) return 'bg-success';
        if (statuses.some(status => status === 'inprogress')) return 'bg-warning';
        return 'bg-secondary';
    };

    const rowClassName = (data) => {
        switch (data.level[0]) {
            case '1': return 'level-1';
            case '2': return 'level-2';
            case '3': return 'level-3';
            case '4': return 'level-4';
            case '5': return 'level-5';
            default: return '';
        }
    };

    const cellClassName = (value) => {
        const numericValue = parseInt(String(value).replace(/[^\d]/g, ''), 10);
        if (numericValue === 0) return '';
        if (numericValue === 1 || numericValue === 2 || numericValue === 3 || numericValue === 4) return 'cell-green';
        if (numericValue === 15 || numericValue === 20 || numericValue === 25 || numericValue === 16) return 'cell-red';
        return 'cell-yellow';
    };

    const cellStyle = (data, field) => cellClassName(data[field]);

    // Mock API functions
    const getRoutineList = async () => {
        try {
            const response = await API.get(RISKASSESSMENT_LIST);
            if (response.status === 200) {
                setRiskRoutine(response.data);
            }
        } catch (error) {
            console.error('Error fetching routine list:', error);
        }
    };

    const getWorkActivity = async () => {
        try {
            const response = await API.get(GMS1_URL);
            if (response.status === 200) {
                const transformedOptions = response.data.map(option => ({
                    label: option.name,
                    value: option.id,
                    workActivities: option.workActivities || []
                }));
                setDepart(transformedOptions);
            }
        } catch (error) {
            console.error('Error fetching work activities:', error);
        }
    };

    const getCrewList = async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: 'ra_member'
            });
            if (response.status === 200) {
                let data = [];
                response.data.map((item) => {
                    if (item.id !== user.id) {
                        data.push({ name: item.firstName, id: item.id });
                    }
                });
                setCrew(data);
            }
        } catch (error) {
            console.error('Error fetching crew list:', error);
        }
    };

    const getHazardList = async () => {
        try {
            const selectedIndustry = localStorage.getItem('SELECTED_INDUSTRIES');

            if (selectedIndustry) {
                const selectedIndustryNames = selectedIndustry
                    .split(',')
                    .map(name => name.trim());

                const uriString = {
                    include: [
                        {
                            relation: "hazardCategories",
                            scope: {
                                include: [{ relation: "hazardItems" }]
                            }
                        }
                    ]
                };

                const response = await API.get(GET_RISK_HAZARD_URL);
                if (response.status === 200) {
                    const industryList = response.data;
                    const matchedIndustries = industryList.filter(item =>
                        selectedIndustryNames.includes(item.name)
                    );
                    const allHazards = matchedIndustries.flatMap(
                        industry => industry.hazardCategories || []
                    );
                    setHazards(allHazards);
                }
            } else {
                const response = await API.get(HAZARDS_CATEGOTY);
                if (response.status === 200) {
                    const data = response.data.filter(
                        (item) => item.name !== 'Hazard-Based'
                    );
                    setHazards(data);
                }
            }
        } catch (error) {
            console.error('Error fetching hazard list:', error);
        }
    };

    const getAllResponsibility = async () => {
        try {
            const response = await API.get(GET_ALL_USER);
            if (response.status === 200) {
                const depart = response.data.map(item => ({
                    id: item.id,
                    name: item.firstName,
                    email: item.email
                }));
                setResponsibility(depart);
            }
        } catch (error) {
            console.error('Error fetching responsibility list:', error);
        }
    };

    const getHighRiskHazardList = async () => {
        try {
            const response = await API.get(RISKASSESSMENT_LIST);
            if (response.status === 200) {
                setRisk(response.data);
            }
        } catch (error) {
            console.error('Error fetching high-risk hazard list:', error);
        }
    };

    const getRiskUpdate = async () => {
        try {
            if (data && data.id) {
                const response = await API.get(RISK_UPDATE_WITH_ID_URL(data.id));
                if (response.status === 200) {
                    setUpdate(response.data);
                }
            }
        } catch (error) {
            console.error("Error fetching risk update:", error);
        }
    };

    // Event handlers
    const handleFileChange = async (files) => {
        setFiles(files);
    };

    const handleDragStart = (event, index) => {
        setDraggedItemIndex(index);
        event.dataTransfer.effectAllowed = 'move';
    };

    const handleDrop = (event, dropIndex) => {
        event.preventDefault();
        const draggedItem = task[draggedItemIndex];
        const updatedTaskList = [...task];
        updatedTaskList.splice(draggedItemIndex, 1);
        updatedTaskList.splice(dropIndex, 0, draggedItem);
        setTask(updatedTaskList);
        setDraggedItemIndex(null);
    };

    const handleDragOver = (event) => {
        event.preventDefault();
    };

    const AddSubActivityTitle = async () => {
        let image = [];

        if (files.length !== 0) {
            for (const item of files) {
                const formData1 = new FormData();
                formData1.append('file', item);

                try {
                    const response = await API.post(FILE_URL, formData1, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        }
                    });

                    if (response && response.status === 200) {
                        image.push(response.data.files[0].originalname);
                    }
                } catch (error) {
                    console.error("File upload error: ", error);
                }
            }
        }

        if (subActivityName !== '') {
            const t = [
                { type: 'activity', name: subActivityName, images: image },
                { type: 'hazards', selected: [] },
                { type: 'consequence', option: [{ value: "", files: [], current_type: '', }] },
                { type: 'current_control', option: [{ value: "", files: [], current_type: '', method: '' }] },
                { type: 'assessment', severity: '', likelyhood: '', level: '' },
                { type: 'additional', accept: true },
                { type: 'responsibility', option: [{ current_type: '', person: '', date: null, value: '' }] },
                { type: 'reassessment', severity: '', likelyhood: '', level: '' },
                { type: 'activeStep', step: 0 },
                { type: 'stage', level: ['Hazards Identification', 'Consequences', 'Current Controls', 'Risk Estimation'] },
                { type: 'completed_stage', level: [] },
                {
                    type: 'status', value: {
                        hazardsIdentification: '',
                        consequences: '',
                        currentControls: '',
                        riskEstimation: '',
                        additionalControls: '',
                    }
                }
            ];
            setTask((prev) => [...prev, t]);
            setSubActivityName('');
            setFiles([]);
            setAddSubActivity(false);
        }
    };

    const deleteTask = (e, i) => {
        e.stopPropagation();
        const newTasks = task.filter((_, idx) => idx !== i);
        setTask(newTasks);
    };

    const openDialog = (item, i) => {
        setItem('');
        setItem(item);
        setIndex(i);
        setVisible(true);
    };

    const subActivity = (item, i) => {
        setItem('');
        setItem(item);
        setIndex(i);
        setSubActivityModal(true);
    };

    const editSubActivityTitle = (name, images) => {
        const updatedTask = task.map((item, i) => {
            if (i === index) {
                return item.map((ite) => {
                    if (ite.type === 'activity') {
                        return { ...ite, name: name, images: images };
                    }
                    return ite;
                });
            }
            return item;
        });
        setTask(updatedTask);
        setItem(updatedTask[index]);
    };

    const onClickHazards = (ha, j) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'hazards') {
                        if (ite.selected.some(hazards => hazards.id === ha.id)) {
                            const index = ite.selected.findIndex(hazard => hazard.id === ha.id);
                            if (index !== -1) {
                                const newHazards = [...ite.selected];
                                newHazards.splice(index, 1);
                                ite.selected = newHazards;
                            }
                        } else {
                            ite.selected.push(ha);
                        }
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const onDeleteHaz = (item1) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'hazards') {
                        const index = ite.selected.findIndex(hazard => hazard.id === item1.id);
                        if (index !== -1) {
                            const newHazards = [...ite.selected];
                            newHazards.splice(index, 1);
                            ite.selected = newHazards;
                        }
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    // More event handlers
    const onChangeSeverity = (e, type) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.severity = e.value;
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const onChangeLikelyhood = (e, type) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.likelyhood = e.value;
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const onImapactOn = (value, j, type) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.current_type = value;
                            }
                        });
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const onMethodOn = (value, j, type) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.method = value;
                            }
                        });
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const onConseqText = (value, j, type) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.value = value;
                            }
                        });
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const onConseqRequired = (value, j, type, type1) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                if (type1 === 'required') {
                                    con.required = !value;
                                } else {
                                    con.validity = !value;
                                }
                            }
                        });
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const onDeleteConseq = (j, type) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        const newHazards = [...ite.option];
                        newHazards.splice(j, 1);
                        ite.option = newHazards;
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const addConsequence = (type) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.push({ value: "", files: [], current_type: '', method: '' });
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const onChangeReAss = (value) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'additional') {
                        ite.accept = value;
                    }
                    if (ite.type === 'stage') {
                        if (value === false) {
                            if (!ite.level.includes('Additional Controls')) {
                                ite.level.push('Additional Controls');
                            }
                        } else {
                            ite.level = ite.level.filter(item => item !== 'Additional Controls');
                        }
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const handleTaskFileChange = async (value, j, type) => {
        if (value.length > 0) {
            const latestFile = value[value.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);

            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    }
                });

                if (response && response.status === 200) {
                    const t = task;
                    const text = t.map((item, i) => {
                        if (i === index) {
                            item.map((ite) => {
                                if (ite.type === type) {
                                    ite.option.map((con, c) => {
                                        if (c === j) {
                                            con.files.push(response.data.files[0].originalname);
                                        }
                                    });
                                }
                            });
                        }
                        return item;
                    });
                    setTask(text);
                    setItem(text[index]);
                }
            } catch (error) {
                console.error("File upload error: ", error);
            }
        }
    };

    const handleRemoveImage = (m, index, type) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === type) {
                        ite.option.map((con, c) => {
                            con.files.splice(m, 1);
                        });
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const onControlAddion = (value, j) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'responsibility') {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.current_type = value;
                            }
                        });
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const onControlAddionText = (value, j) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'responsibility') {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.value = value;
                            }
                        });
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const onResponsePerson = (value, j) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'responsibility') {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.person = value;
                            }
                        });
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const onResponseDate = (value, j) => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'responsibility') {
                        ite.option.map((con, c) => {
                            if (c === j) {
                                con.date = value;
                            }
                        });
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const addAdditionalControl = () => {
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'responsibility') {
                        ite.option.push({ current_type: '', person: '', date: null, value: '' });
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const checkRequiredStepField = () => {
        let required = true;
        if (item[8].step === 0) {
            if (item[1].selected.length === 0) {
                required = false;
                setRequired(false);
            }
        } else if (item[8].step === 1) {
            for (let option of item[2].option) {
                if (option.value === "" && option.current_type === '') {
                    required = false;
                    setRequired(false);
                }
            }
        } else if (item[8].step === 2) {
            for (let option of item[3].option) {
                if (option.value === "" && option.current_type === '') {
                    required = false;
                    setRequired(false);
                }
            }
        } else if (item[8].step === 3) {
            if (item[4].severity === '' && item[4].likelyhood === '') {
                required = false;
                setRequired(false);
            }
        } else if (item[8].step === 4) {
            if (item[7].severity === '' && item[7].likelyhood === '') {
                required = false;
                setRequired(false);
            }
        }
        return required;
    };

    const sectionNames = [
        "Hazard Identification",
        "Consequences",
        "Current Controls",
        "Risk Estimation",
        "Additional Controls"
    ];

    const handleNext = () => {
        if (checkRequiredStepField()) {
            item[10].level.push(item[8].step);
            item[10].level = [...new Set(item[10].level)];

            const updatedTask = task.map((item, i) => {
                if (i === index) {
                    item.map((ite) => {
                        if (ite.type === 'status') {
                            switch (item[8].step) {
                                case 0:
                                    ite.value.hazardsIdentification = 'completed';
                                    break;
                                case 1:
                                    ite.value.consequences = 'completed';
                                    break;
                                case 2:
                                    ite.value.currentControls = 'completed';
                                    break;
                                case 3:
                                    ite.value.riskEstimation = 'completed';
                                    break;
                                case 4:
                                    ite.value.additionalControls = 'completed';
                                    break;
                                default:
                                    break;
                            }
                        }
                    });
                }
                return item;
            });

            setTask(updatedTask);
            setItem(updatedTask[index]);

            if (item && item[8] && typeof item[8].step !== 'undefined') {
                const currentSectionName = sectionNames[item[8].step];
                Swal.fire({
                    toast: true,
                    icon: 'success',
                    title: 'Saved & Finalized',
                    text: `${currentSectionName} saved & finalized.`,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                });
            }
        } else {
            alert('Please fill in the required fields');
        }
    };

    const saveProgress = () => {
        const updatedTask = task.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'status') {
                        switch (item[8].step) {
                            case 0:
                                ite.value.hazardsIdentification = 'inprogress';
                                break;
                            case 1:
                                ite.value.consequences = 'inprogress';
                                break;
                            case 2:
                                ite.value.currentControls = 'inprogress';
                                break;
                            case 3:
                                ite.value.riskEstimation = 'inprogress';
                                break;
                            case 4:
                                ite.value.additionalControls = 'inprogress';
                                break;
                            default:
                                break;
                        }
                    }
                });
            }
            return item;
        });

        setTask(updatedTask);
        setItem(updatedTask[index]);

        if (item && item[8] && typeof item[8].step !== 'undefined') {
            const currentSectionName = sectionNames[item[8].step];
            Swal.fire({
                toast: true,
                icon: 'success',
                title: 'Saved as Draft',
                text: `${currentSectionName} saved as draft.`,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            });
        }
    };

    const handleStageClick = (step) => {
        item[10].level = item[10].level.filter(item1 => item1 !== item[8].step);
        const t = task;
        const text = t.map((item, i) => {
            if (i === index) {
                item.map((ite) => {
                    if (ite.type === 'activeStep') {
                        ite.step = step;
                    }
                });
            }
            return item;
        });
        setTask(text);
        setItem(text[index]);
    };

    const onSubmitUpdate = () => {
        getRiskUpdate();
        // editUserHandler(); // This would be implemented based on your needs
    };

    // useEffect hooks
    useEffect(() => {
        const fetchData = async () => {
            try {
                await Promise.all([
                    getRoutineList(),
                    getWorkActivity(),
                    getCrewList(),
                    getHazardList(),
                    getAllResponsibility(),
                    getHighRiskHazardList()
                ]);
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };

        fetchData();
    }, []);

    useEffect(() => {
        setOnEdit(true);
    }, [domain === "edit"]);

    useEffect(() => {
        if (selectedDepart !== null) {
            setActivity([]);
            const active = depart.find(item => item.value === selectedDepart?.value);
            if (active && active.workActivities) {
                const workActivityIdSet = new Set(riskRoutine.map(item => item.workActivityId));
                const filteredWorkActivities = active.workActivities.filter(activity => !workActivityIdSet.has(activity.id));

                if (filteredWorkActivities) {
                    const transformedOptions = filteredWorkActivities.map(option => ({
                        label: option.name,
                        value: option.id,
                    }));
                    setActivity(transformedOptions);
                }
            } else {
                setActivity([]);
            }
        }
    }, [selectedDepart]);

    const footerTemplate = (
        <div className="d-flex justify-content-between align-items-center">
            {item && item[8] && typeof item[8].step !== 'undefined' && (
                <>
                    <div className="d-flex">
                        <Button
                            className='me-2'
                            outlined
                            label={`Save Progress`}
                            onClick={saveProgress}
                        />
                        <Button
                            label={`Save & Finalize ${sectionNames[item[8].step]} for Sub Activity`}
                            onClick={handleNext}
                        />
                    </div>
                </>
            )}
        </div>
    );

    return (
        <div className="container-fluid">
            {isLoading && <FullLoader />}

            <div className="row">
                <div className="col-12">
                    <h2>Risk Assessment - Routine</h2>
                </div>
            </div>

            {/* Department and Activity Selection */}
            <div className="row mt-4">
                <div className="col-6">
                    <label>Department</label>
                    <Dropdown
                        value={selectedDepart}
                        options={depart}
                        onChange={(e) => setSelectedDepart(e.value)}
                        placeholder="Select Department"
                        className="w-100"
                    />
                </div>
                <div className="col-6">
                    <label>Work Activity</label>
                    <Dropdown
                        value={selectedActivity}
                        options={activity}
                        onChange={(e) => setSelectedActivity(e.value)}
                        placeholder="Select Work Activity"
                        className="w-100"
                    />
                </div>
            </div>

            {/* Activity Description */}
            <div className="row mt-4">
                <div className="col-12">
                    <label>Activity Description</label>
                    <InputTextarea
                        value={activityDesc}
                        onChange={(e) => setActivityDesc(e.target.value)}
                        rows={3}
                        className="w-100"
                    />
                </div>
            </div>

            {/* Team Members */}
            <div className="row mt-4">
                <div className="col-12">
                    <label>RA Team Members</label>
                    <MultiSelect
                        value={selectedCrew}
                        options={crew}
                        onChange={(e) => setSelectedCrew(e.value)}
                        optionLabel="name"
                        placeholder="Select Team Members"
                        className="w-100"
                    />
                </div>
            </div>

            {/* Add Sub Activity */}
            <div className="row mt-4">
                <div className="col-12">
                    <Button
                        label="Add Sub Activity"
                        onClick={() => setAddSubActivity(true)}
                        className="mb-3"
                    />
                </div>
            </div>

            {/* Sub Activity Modal */}
            {addSubActivity && (
                <Modal show={addSubActivity} onHide={() => setAddSubActivity(false)}>
                    <Modal.Header closeButton>
                        <Modal.Title>Add Sub Activity</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <div className="mb-3">
                            <label>Sub Activity Name</label>
                            <InputText
                                value={subActivityName}
                                onChange={(e) => setSubActivityName(e.target.value)}
                                className="w-100"
                            />
                        </div>
                        <div className="mb-3">
                            <label>Upload Images</label>
                            <DropzoneArea
                                acceptedFiles={['image/jpeg', 'image/png']}
                                dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                                filesLimit={5}
                                maxFileSize={104857600}
                                onChange={handleFileChange}
                                showPreviewsInDropzone={false}
                                showPreviews={false}
                            />
                        </div>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button label="Cancel" onClick={() => setAddSubActivity(false)} outlined />
                        <Button label="Add" onClick={AddSubActivityTitle} />
                    </Modal.Footer>
                </Modal>
            )}

            {/* Task List */}
            <div className="row mt-4">
                <div className="col-12">
                    <h4>Sub Activities</h4>
                    {task.map((taskItem, index) => (
                        <TaskItem
                            key={index}
                            task={taskItem}
                            index={index}
                            openDialog={openDialog}
                            deleteTask={deleteTask}
                            subActivity={subActivity}
                            handleDragStart={handleDragStart}
                            handleDrop={handleDrop}
                            handleDragOver={handleDragOver}
                            draggedItemIndex={draggedItemIndex}
                            getStatusClass={getStatusClass}
                        />
                    ))}
                </div>
            </div>

            {/* Risk Assessment Modal */}
            {item !== '' && (
                <Modal show={visible} onHide={() => setVisible(false)} size='lg'>
                    <Modal.Header closeButton>
                        <Modal.Title>Sub-activity Risk Assessment</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <SubActivityComponent
                            item={item}
                            onSave={editSubActivityTitle}
                        />

                        <hr />
                        {item !== '' && item[8] && item[8].step !== undefined && (
                            <HeadStepper
                                activeStage={item[8].step}
                                stages={item[9].level}
                                stageStatus={item[11].value}
                                handleStageClick={handleStageClick}
                                getStatusClass={getStatusClass}
                            />
                        )}
                        <hr />

                        {/* Hazards Identification Step */}
                        {item[8].step === 0 && (
                            <>
                                <HazardAccordion
                                    hazards={hazards}
                                    activeTabIndex={activeTabIndex}
                                    setActiveTabIndex={setActiveTabIndex}
                                    selectedHazards={item[1].selected}
                                    onClickHazards={onClickHazards}
                                    required={required}
                                    item={item}
                                />
                                <IdentifiedHazards
                                    selectedHazards={item[1].selected}
                                    onDeleteHaz={onDeleteHaz}
                                />
                            </>
                        )}

                        {/* Consequences Step */}
                        {item[8].step === 1 && (
                            <>
                                <h6 className='fw-bold'>Consequences</h6>
                                <p>For this sub-activity, list and elaborate on all potential consequences associated with the identified hazards, covering impacts on Personnel, Environment, Property/Equipment, and Operations, as applicable.</p>
                                {item[2].option.map((con, i) => (
                                    <Consequence
                                        key={i}
                                        con={con}
                                        i={i}
                                        impactOn={impactOn}
                                        onImapactOn={onImapactOn}
                                        onConseqText={onConseqText}
                                        onDeleteConseq={onDeleteConseq}
                                        handleTaskFileChange={handleTaskFileChange}
                                        required={required}
                                        type={'routine'}
                                        handleRemoveImage={handleRemoveImage}
                                    />
                                ))}
                                <Button
                                    label="Add Consequence"
                                    onClick={() => addConsequence('consequence')}
                                    className="mt-3"
                                    outlined
                                />
                            </>
                        )}

                        {/* Current Controls Step */}
                        {item[8].step === 2 && (
                            <>
                                <h6 className='fw-bold'>Current Controls</h6>
                                <p>Identify and describe the current controls in place to manage the identified hazards and consequences.</p>
                                {item[3].option.map((con, i) => (
                                    <CurrentControl
                                        key={i}
                                        con={con}
                                        i={i}
                                        control={control}
                                        controlType={controlType}
                                        onImapactOn={onImapactOn}
                                        onConseqText={onConseqText}
                                        onDeleteConseq={onDeleteConseq}
                                        onConseqRequired={onConseqRequired}
                                        handleTaskFileChange={handleTaskFileChange}
                                        onMethodOn={onMethodOn}
                                        required={required}
                                        type={'routine'}
                                        handleRemoveMainImage={handleRemoveImage}
                                    />
                                ))}
                                <Button
                                    label="Add Current Control"
                                    onClick={() => addConsequence('current_control')}
                                    className="mt-3"
                                    outlined
                                />
                            </>
                        )}

                        {/* Risk Assessment Step */}
                        {item[8].step === 3 && (
                            <RiskAssessment
                                item={item}
                                severity={severity}
                                severityData={severityData}
                                required={required}
                                onChangeSeverity={onChangeSeverity}
                                likelyhood={likelyhood}
                                levelData={levelData}
                                onChangeLikelyhood={onChangeLikelyhood}
                                rowClassName={rowClassName}
                                tableData={tableData}
                                cellClassName={cellClassName}
                                cellStyle={cellStyle}
                                onChangeReAss={onChangeReAss}
                            />
                        )}

                        {/* Additional Controls Step */}
                        {item[8].step === 4 && (
                            <ProposedRiskManagement
                                item={item}
                                controlAdditional={controlAdditional}
                                controlType={controlType}
                                onControlAddion={onControlAddion}
                                onControlAddionText={onControlAddionText}
                                onResponsePerson={onResponsePerson}
                                onResponseDate={onResponseDate}
                                responsibility={responsibility}
                                addAdditionalControl={addAdditionalControl}
                                onDeleteConseq={onDeleteConseq}
                                required={required}
                            />
                        )}
                    </Modal.Body>
                    <Modal.Footer>
                        {footerTemplate}
                    </Modal.Footer>
                </Modal>
            )}

            {/* Risk Updates */}
            <div className="row mt-4">
                <div className="col-12">
                    <RiskUpdate updates={Update} onSubmitUpdate={onSubmitUpdate} />
                </div>
            </div>

            {/* Update Table */}
            <div className="row mt-4">
                <div className="col-12">
                    <UpdateTable updates={Update} />
                </div>
            </div>
        </div>
    );
}

// Test wrapper component to demonstrate the functionality
const TestApp = () => {
    // Mock data for testing
    const mockData = {
        id: 1,
        departmentId: 1,
        workActivityId: 1,
        description: 'Test Risk Assessment',
        tasks: [],
        additonalRemarks: '',
        overallRecommendationOne: null,
        overallRecommendationTwo: null,
        raTeamMembers: [],
        nonRoutineWorkActivity: '',
        nonRoutineDepartment: '',
        hazardName: '',
        highRisk: [],
        shortName: 'TEST-RA-001'
    };

    return (
        <div className="container-fluid p-4">
            <div className="row">
                <div className="col-12">
                    <div className="alert alert-info">
                        <h4>Risk Assessment Test Page</h4>
                        <p>This is a consolidated version of the Routine.js component with all dependencies included in a single file.</p>
                        <p>All components from the original file have been integrated without external imports.</p>
                    </div>
                </div>
            </div>

            <Routine
                data={mockData}
                type="routine"
                domain="create"
            />
        </div>
    );
};

export default TestApp;
export { Routine };
