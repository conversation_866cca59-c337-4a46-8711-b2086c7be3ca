import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import PageHeader from '@/components/common/PageHeader';
import TabsContainer from '@/components/common/TabsContainer';
import ExpandableDataTable from '@/components/common/ExpandableDataTable';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Search,
  Plus,
  Download,
  FileWarning,
  Calendar,
  ClipboardList,
  User,
  Info,
  CheckCircle,
  AlertCircle,
  Clock,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { fetchRiskAssessments, RiskAssessmentResponse } from '@/services/api';
import { formatDate, formatDateReadable } from '@/utils/dateUtils';

// Mock data types
interface RiskRegisterItem {
  id: string;
  assessmentType: 'Routine' | 'Non-Routine' | 'High Risk';
  title: string;
  category: string;
  status: 'Draft' | 'Pending' | 'Published';
  createdBy: string;
  createdDate: string;
  lastUpdated: string;
  riskLevel: 'Low' | 'Medium' | 'High' | 'Critical';
}

interface ControlItem {
  id: string;
  control: string;
  category: string;
  associatedRisk: string;
  implementationDate: string;
  status: string;
  effectiveness: 'Low' | 'Medium' | 'High';
  responsibleParty: string;
}

interface ToolBoxTalkItem {
  id: string;
  title: string;
  date: string;
  location: string;
  participants: number;
  conductor: string;
  status: 'Scheduled' | 'Completed' | 'Cancelled';
}

interface ToolBoxTalkFlag {
  id: string;
  tbtId: string;
  issue: string;
  raisedBy: string;
  raisedDate: string;
  status: 'New' | 'Under Review' | 'Addressed' | 'Closed';
  priority: 'Low' | 'Medium' | 'High';
  associatedRiskId: string;
}

interface MyAction {
  id: string;
  title: string;
  type: string;
  dueDate: string;
  status: string;
  priority: string;
  assignedBy: string;
}

// Mock data
const mockMyActions: MyAction[] = [
  {
    id: 'ACT-001',
    title: 'Review high-risk assessment for chemical handling',
    type: 'Risk Assessment',
    dueDate: '2023-06-15',
    status: 'Pending',
    priority: 'High',
    assignedBy: 'John Smith'
  },
  {
    id: 'ACT-002',
    title: 'Implement new controls for working at heights',
    type: 'Control Implementation',
    dueDate: '2023-06-20',
    status: 'In Progress',
    priority: 'Medium',
    assignedBy: 'Sarah Johnson'
  },
  {
    id: 'ACT-003',
    title: 'Conduct Tool Box Talk on electrical safety',
    type: 'Tool Box Talk',
    dueDate: '2023-06-10',
    status: 'Completed',
    priority: 'Medium',
    assignedBy: 'Michael Brown'
  }
];

const mockRiskRegister: RiskRegisterItem[] = [
  {
    id: 'RA-001',
    assessmentType: 'Routine',
    title: 'Chemical Handling in Laboratory',
    category: 'Hazardous Materials',
    status: 'Published',
    createdBy: 'John Smith',
    createdDate: '2023-05-01',
    lastUpdated: '2023-05-15',
    riskLevel: 'High'
  },
  {
    id: 'RA-002',
    assessmentType: 'High Risk',
    title: 'Working at Heights - Scaffold Assembly',
    category: 'Fall Protection',
    status: 'Pending',
    createdBy: 'Sarah Johnson',
    createdDate: '2023-05-10',
    lastUpdated: '2023-05-10',
    riskLevel: 'Critical'
  },
  {
    id: 'RA-003',
    assessmentType: 'Non-Routine',
    title: 'Confined Space Entry - Storage Tanks',
    category: 'Confined Spaces',
    status: 'Draft',
    createdBy: 'Michael Brown',
    createdDate: '2023-05-20',
    lastUpdated: '2023-05-20',
    riskLevel: 'High'
  }
];

const mockExistingControls: ControlItem[] = [
  {
    id: 'CTL-001',
    control: 'Chemical-resistant gloves and face shields',
    category: 'PPE',
    associatedRisk: 'RA-001',
    implementationDate: '2023-01-15',
    status: 'Active',
    effectiveness: 'High',
    responsibleParty: 'Lab Safety Team'
  },
  {
    id: 'CTL-002',
    control: 'Fall arrest systems for all workers above 6ft',
    category: 'Engineering',
    associatedRisk: 'RA-002',
    implementationDate: '2023-02-10',
    status: 'Active',
    effectiveness: 'High',
    responsibleParty: 'Construction Safety'
  },
  {
    id: 'CTL-003',
    control: 'Ventilation system for confined spaces',
    category: 'Engineering',
    associatedRisk: 'RA-003',
    implementationDate: '2023-03-05',
    status: 'Active',
    effectiveness: 'Medium',
    responsibleParty: 'Maintenance Team'
  }
];

const mockPlannedControls: ControlItem[] = [
  {
    id: 'PCTL-001',
    control: 'Automated chemical dispensing system',
    category: 'Engineering',
    associatedRisk: 'RA-001',
    implementationDate: '2023-08-15',
    status: 'Planned',
    effectiveness: 'High',
    responsibleParty: 'Lab Safety Team'
  },
  {
    id: 'PCTL-002',
    control: 'Enhanced guardrail system with toe boards',
    category: 'Engineering',
    associatedRisk: 'RA-002',
    implementationDate: '2023-07-20',
    status: 'In Progress',
    effectiveness: 'High',
    responsibleParty: 'Construction Safety'
  },
  {
    id: 'PCTL-003',
    control: 'Gas monitoring system with remote alerts',
    category: 'Engineering',
    associatedRisk: 'RA-003',
    implementationDate: '2023-09-10',
    status: 'Planned',
    effectiveness: 'High',
    responsibleParty: 'Maintenance Team'
  }
];

const mockToolBoxTalks: ToolBoxTalkItem[] = [
  {
    id: 'TBT-001',
    title: 'Chemical Handling Safety',
    date: '2023-05-05',
    location: 'Main Laboratory',
    participants: 12,
    conductor: 'John Smith',
    status: 'Completed'
  },
  {
    id: 'TBT-002',
    title: 'Fall Protection Awareness',
    date: '2023-05-12',
    location: 'Construction Site A',
    participants: 8,
    conductor: 'Sarah Johnson',
    status: 'Completed'
  },
  {
    id: 'TBT-003',
    title: 'Confined Space Entry Procedures',
    date: '2023-06-10',
    location: 'Storage Area',
    participants: 6,
    conductor: 'Michael Brown',
    status: 'Scheduled'
  }
];

const mockToolBoxTalkFlags: ToolBoxTalkFlag[] = [
  {
    id: 'FLAG-001',
    tbtId: 'TBT-001',
    issue: 'Inadequate spill response equipment identified',
    raisedBy: 'Emma Wilson',
    raisedDate: '2023-05-05',
    status: 'Addressed',
    priority: 'High',
    associatedRiskId: 'RA-001'
  },
  {
    id: 'FLAG-002',
    tbtId: 'TBT-002',
    issue: 'Workers reported damaged harnesses',
    raisedBy: 'Robert Davis',
    raisedDate: '2023-05-12',
    status: 'Closed',
    priority: 'High',
    associatedRiskId: 'RA-002'
  },
  {
    id: 'FLAG-003',
    tbtId: 'TBT-001',
    issue: 'Chemical storage cabinet ventilation inadequate',
    raisedBy: 'John Smith',
    raisedDate: '2023-05-05',
    status: 'Under Review',
    priority: 'Medium',
    associatedRiskId: 'RA-001'
  }
];

const RiskAssessmentPage = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("my-actions");
  const [riskRegister, setRiskRegister] = useState<RiskRegisterItem[]>(mockRiskRegister);
  const [riskAssessments, setRiskAssessments] = useState<RiskAssessmentResponse[]>([]);
  const [loading, setLoading] = useState({
    riskAssessments: false,
  });
  const [error, setError] = useState<string | null>(null);
  const [riskAssessmentCount, setRiskAssessmentCount] = useState(0);

  // Fetch risk assessments from API
  const fetchRiskAssessmentsData = async () => {
    setLoading(prev => ({ ...prev, riskAssessments: true }));
    setError(null);

    try {
      const accessToken = localStorage.getItem('access_token') || '';
      const response = await fetchRiskAssessments(accessToken);
      setRiskAssessments(response);
      setRiskAssessmentCount(response.length);
    } catch (err) {
      setError('Failed to fetch risk assessments.');
      console.error(err);
    } finally {
      setLoading(prev => ({ ...prev, riskAssessments: false }));
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchRiskAssessmentsData();
  }, []);

  // Help text for each tab
  const helpTexts = {
    riskRegister: "The Risk Register catalogs all activities and scenarios requiring risk assessments, including routine operations, non-routine tasks, and high-risk scenarios. Entries progress through three states:\n\nDraft: Editable by RA Team Leaders (click the ID to refine).\n\nPending: Published for team affirmation—visible to all RA Team Members but locked for edits.\n\nPublished: Finalized and accessible to all assigned platform users.\nRA Team Members can view Draft/Pending entries for transparency, while Published risk assessments include a Download button (top-right) for easy sharing and compliance.",

    existingControls: "This register serves as a centralized repository of all operational controls identified during Risk Assessments (RAs) conducted on the platform. It dynamically updates as controls are approved in RAs, providing visibility into safeguards currently in place to mitigate risks across routine activities, non-routine tasks, and high-risk scenarios.\nPurpose:\n\nTrack and manage controls to ensure compliance with organizational policies and regulatory standards.\n\nEnable teams to reference, review, and audit existing safeguards during audits or workflow updates.",

    plannedControls: "This register tracks additional controls identified during Risk Assessments (RAs) that are pending implementation. These controls are critical for addressing gaps in risk mitigation but have not yet been integrated into operational workflows due to dependencies, resource constraints, or scheduled timelines.\nPurpose:\n\nMonitor progress toward closing risk gaps and ensure accountability for implementation.\n\nPrioritize actions to align with compliance deadlines or operational needs.",

    toolBoxTalks: "This table provides a centralized digital record of all safety briefings (Tool Box Talks) conducted on the AcuiZen platform. Click on the TBT ID field to view detailed records, including attachments, filled out checklists and other actions.",

    toolBoxTalkFlags: "This table captures real-world insights from safety briefings / TBT to improve Risk Assessments (RAs) and build a centralized repository of practical learnings. Information logged here serves as critical feedback for RA amendments, highlighting gaps between planned controls and on-ground realities (e.g., unanticipated hazards, procedural challenges). RA Team Leaders can review these flags, edit entries, and document actions taken—such as updating controls or revising workflows—to address flagged issues. Over time, this repository helps quantify RA effectiveness; frequent flags may indicate shallow assessments, while resolved flags demonstrate iterative improvements. Metrics from this module (e.g., flag frequency, type) will feed into dashboards to track RA practicality and compliance."
  };

  // Handle row click for different tables
  const handleRowClick = (id: string, type: string) => {
    toast({
      title: `${type} Details`,
      description: `Viewing details for ${id}`,
    });
  };

  // Handle risk assessment form submission
  const handleRiskAssessmentSubmit = (data: any, isDraft: boolean) => {
    // Create a new risk register item from the form data
    const newRiskItem: RiskRegisterItem = {
      id: data.id,
      assessmentType: data.assessmentType,
      title: `${DEPARTMENTS.find(d => d.value === data.department)?.label || ''} - ${WORK_ACTIVITIES.find(w => w.value === data.workActivity)?.label || ''}`,
      category: WORK_ACTIVITIES.find(w => w.value === data.workActivity)?.label || '',
      status: isDraft ? 'Draft' : 'Pending',
      createdBy: 'Current User',
      createdDate: data.createdDate,
      lastUpdated: data.lastUpdated,
      riskLevel: data.recommendationLevel === 'high-risk' ? 'High' :
                data.recommendationLevel === 'medium-risk' ? 'Medium' : 'Low',
    };

    // Add the new item to the risk register
    setRiskRegister([newRiskItem, ...riskRegister]);

    // Show success message
    toast({
      title: isDraft ? "Draft Saved" : "Risk Assessment Submitted",
      description: `${newRiskItem.title} has been ${isDraft ? 'saved as draft' : 'released for affirmation'}.`,
    });

    // If not a draft, switch to the risk register tab
    if (!isDraft) {
      setActiveTab("risk-register");
    }
  };

  // Mock data for dropdowns (should match the ones in AddRiskAssessmentModal)
  const DEPARTMENTS = [
    { label: 'Operations', value: 'operations' },
    { label: 'Maintenance', value: 'maintenance' },
    { label: 'Safety', value: 'safety' },
    { label: 'Quality Control', value: 'quality-control' },
    { label: 'Logistics', value: 'logistics' },
  ];

  const WORK_ACTIVITIES = [
    { label: 'Chemical Handling', value: 'chemical-handling' },
    { label: 'Working at Heights', value: 'working-at-heights' },
    { label: 'Confined Space Entry', value: 'confined-space-entry' },
    { label: 'Electrical Work', value: 'electrical-work' },
    { label: 'Hot Work', value: 'hot-work' },
    { label: 'Lifting Operations', value: 'lifting-operations' },
  ];

  // Render help text tooltip
  const renderHelpText = (tabKey: string) => {
    const helpText = helpTexts[tabKey];
    if (!helpText) return null;

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="inline-flex">
              <Info className="h-4 w-4 text-muted-foreground cursor-pointer" />
            </div>
          </TooltipTrigger>
          <TooltipContent side="right" className="max-w-md p-4">
            <div className="text-sm whitespace-pre-line">{helpText}</div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  // Define tabs for the TabsContainer
  const tabs = [
    {
      value: "my-actions",
      label: "My Actions",
      icon: <ClipboardList className="h-4 w-4" />,
      content: (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search actions..."
                className="pl-8"
              />
            </div>
          </div>

          <ExpandableDataTable
            data={mockMyActions}
            columns={[
              { key: 'id', header: 'ID' },
              { key: 'title', header: 'Title' },
              { key: 'type', header: 'Type' },
              { key: 'dueDate', header: 'Due Date' },
              {
                key: 'status',
                header: 'Status',
                render: (value) => {
                  let badgeClass = '';
                  switch(value) {
                    case 'Pending': badgeClass = 'bg-warning-400 hover:bg-warning-500 text-black'; break;
                    case 'In Progress': badgeClass = 'bg-primary hover:bg-primary/90'; break;
                    case 'Completed': badgeClass = 'bg-success-500 hover:bg-success-600'; break;
                    default: badgeClass = 'bg-muted hover:bg-muted/90';
                  }
                  return <Badge className={badgeClass}>{value}</Badge>;
                }
              },
              { key: 'priority', header: 'Priority' },
            ]}
            onRowClick={(row) => handleRowClick(row.id, 'Action')}
            highlightOnHover={true}
            striped={true}
          />
        </div>
      )
    },
    {
      value: "risk-register",
      label: `Risk Register (${riskAssessmentCount})`,
      icon: <FileWarning className="h-4 w-4" />,
      content: (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search risk register..."
                className="pl-8"
              />
            </div>
            <Button
              className="flex items-center gap-1"
              onClick={() => navigate('/risk-assessment/add')}
            >
              <Plus className="h-4 w-4" /> New Risk Assessment
            </Button>
          </div>

          {loading.riskAssessments ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                <p className="text-muted-foreground">Loading risk assessments...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-4" />
                <p className="text-destructive mb-2">{error}</p>
                <Button onClick={fetchRiskAssessmentsData} variant="outline">
                  Try Again
                </Button>
              </div>
            </div>
          ) : (
            <ExpandableDataTable
              data={riskAssessments}
              columns={[
                {
                  key: 'maskId',
                  header: 'ID',
                  sortable: true,
                  render: (value) => (
                    <span className="text-blue-600 hover:text-blue-800 hover:underline font-medium cursor-pointer">
                      {value}
                    </span>
                  )
                },
                {
                  key: 'workActivity.name',
                  header: 'Process/Activity/High-Risk Scenarios',
                  render: (value, row) => row.workActivity?.name || 'N/A'
                },
                {
                  key: 'department.name',
                  header: 'Operational Risk Area',
                  render: (value, row) => row.department?.name || 'N/A'
                },
                {
                  key: 'type',
                  header: 'Type',
                  filterable: true,
                  filterType: 'select',
                  filterOptions: [
                    { label: 'Routine', value: 'Routine' },
                    { label: 'Non Routine', value: 'Non Routine' },
                    { label: 'High-Risk Hazard', value: 'High-Risk Hazard' }
                  ],
                  render: (value) => {
                    let badgeClass = '';
                    switch(value) {
                      case 'Routine': badgeClass = 'bg-blue-500 hover:bg-blue-600'; break;
                      case 'Non Routine': badgeClass = 'bg-orange-500 hover:bg-orange-600'; break;
                      case 'High-Risk Hazard': badgeClass = 'bg-red-500 hover:bg-red-600'; break;
                      default: badgeClass = 'bg-muted hover:bg-muted/90';
                    }
                    return <Badge className={badgeClass}>{value}</Badge>;
                  }
                },
                {
                  key: 'created',
                  header: 'Initiated Date',
                  render: (value) => formatDate(value)
                },
                {
                  key: 'publishedDate',
                  header: 'Published / Amended Date',
                  render: (value) => formatDate(value)
                },
                {
                  key: 'nextReviewDate',
                  header: 'Next Review Date',
                  render: (value) => formatDate(value)
                },
                {
                  key: 'status',
                  header: 'Status',
                  filterable: true,
                  filterType: 'select',
                  filterOptions: [
                    { label: 'Draft', value: 'Draft' },
                    { label: 'Pending', value: 'Pending' },
                    { label: 'Published', value: 'Published' }
                  ],
                  render: (value) => {
                    let badgeClass = '';
                    switch(value) {
                      case 'Draft': badgeClass = 'bg-muted-foreground hover:bg-gray-600'; break;
                      case 'Pending': badgeClass = 'bg-warning-400 hover:bg-warning-500 text-black'; break;
                      case 'Published': badgeClass = 'bg-success-500 hover:bg-success-600'; break;
                      default: badgeClass = 'bg-muted hover:bg-muted/90';
                    }
                    return <Badge className={badgeClass}>{value}</Badge>;
                  }
                },
                {
                  key: 'teamLeader.firstName',
                  header: 'RA Leader',
                  filterable: true,
                  render: (value, row) => {
                    const leader = row.teamLeader;
                    if (!leader) return 'N/A';
                    return `${leader.firstName}${leader.lastName ? ` ${leader.lastName}` : ''}`;
                  }
                }
              ]}
              onRowClick={(row) => handleRowClick(row.maskId, 'Risk Assessment')}
              highlightOnHover={true}
              striped={true}
            />
          )}
        </div>
      )
    },
    {
      value: "existing-controls",
      label: "Existing Operational Controls Register",
      icon: <CheckCircle className="h-4 w-4" />,
      content: (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search existing controls..."
                className="pl-8"
              />
            </div>
            <Button className="flex items-center gap-1" variant="outline">
              <Download className="h-4 w-4" /> Export Controls
            </Button>
          </div>

          <ExpandableDataTable
            data={mockExistingControls}
            columns={[
              { key: 'id', header: 'ID' },
              { key: 'control', header: 'Control Measure' },
              { key: 'category', header: 'Category' },
              { key: 'associatedRisk', header: 'Risk ID' },
              { key: 'implementationDate', header: 'Implementation Date' },
              {
                key: 'effectiveness',
                header: 'Effectiveness',
                render: (value) => {
                  let badgeClass = '';
                  switch(value) {
                    case 'Low': badgeClass = 'bg-destructive hover:bg-destructive/90'; break;
                    case 'Medium': badgeClass = 'bg-warning-400 hover:bg-warning-500 text-black'; break;
                    case 'High': badgeClass = 'bg-success-500 hover:bg-success-600'; break;
                    default: badgeClass = 'bg-muted hover:bg-muted/90';
                  }
                  return <Badge className={badgeClass}>{value}</Badge>;
                }
              },
              { key: 'responsibleParty', header: 'Responsible Party' },
            ]}
            onRowClick={(row) => handleRowClick(row.id, 'Control')}
            highlightOnHover={true}
            striped={true}
          />
        </div>
      )
    },
    {
      value: "planned-controls",
      label: "Planned Operational Controls Register",
      icon: <Clock className="h-4 w-4" />,
      content: (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search planned controls..."
                className="pl-8"
              />
            </div>
            <Button className="flex items-center gap-1" variant="outline">
              <Download className="h-4 w-4" /> Export Controls
            </Button>
          </div>

          <ExpandableDataTable
            data={mockPlannedControls}
            columns={[
              { key: 'id', header: 'ID' },
              { key: 'control', header: 'Control Measure' },
              { key: 'category', header: 'Category' },
              { key: 'associatedRisk', header: 'Risk ID' },
              { key: 'implementationDate', header: 'Target Date' },
              {
                key: 'status',
                header: 'Status',
                render: (value) => {
                  let badgeClass = '';
                  switch(value) {
                    case 'Planned': badgeClass = 'bg-muted-foreground hover:bg-gray-600'; break;
                    case 'In Progress': badgeClass = 'bg-primary hover:bg-primary/90'; break;
                    case 'Implemented': badgeClass = 'bg-success-500 hover:bg-success-600'; break;
                    default: badgeClass = 'bg-muted hover:bg-muted/90';
                  }
                  return <Badge className={badgeClass}>{value}</Badge>;
                }
              },
              { key: 'responsibleParty', header: 'Responsible Party' },
            ]}
            onRowClick={(row) => handleRowClick(row.id, 'Planned Control')}
            highlightOnHover={true}
            striped={true}
            showActions={true}
            onEdit={(item) => toast({ title: "Edit Planned Control", description: `Editing planned control ${item.id}` })}
          />
        </div>
      )
    },
    {
      value: "toolbox-talks",
      label: "Tool Box Talk Records",
      icon: <ClipboardList className="h-4 w-4" />,
      content: (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search tool box talks..."
                className="pl-8"
              />
            </div>
            <Button className="flex items-center gap-1">
              <Plus className="h-4 w-4" /> New Tool Box Talk
            </Button>
          </div>

          <ExpandableDataTable
            data={mockToolBoxTalks}
            columns={[
              { key: 'id', header: 'TBT ID' },
              { key: 'title', header: 'Title' },
              { key: 'date', header: 'Date' },
              { key: 'location', header: 'Location' },
              { key: 'participants', header: 'Participants' },
              { key: 'conductor', header: 'Conducted By' },
              {
                key: 'status',
                header: 'Status',
                render: (value) => {
                  let badgeClass = '';
                  switch(value) {
                    case 'Scheduled': badgeClass = 'bg-primary hover:bg-primary/90'; break;
                    case 'Completed': badgeClass = 'bg-success-500 hover:bg-success-600'; break;
                    case 'Cancelled': badgeClass = 'bg-destructive hover:bg-destructive/90'; break;
                    default: badgeClass = 'bg-muted hover:bg-muted/90';
                  }
                  return <Badge className={badgeClass}>{value}</Badge>;
                }
              },
            ]}
            onRowClick={(row) => handleRowClick(row.id, 'Tool Box Talk')}
            highlightOnHover={true}
            striped={true}
            showActions={true}
            onEdit={(item) => toast({ title: "Edit Tool Box Talk", description: `Editing tool box talk ${item.id}` })}
          />
        </div>
      )
    },
    {
      value: "toolbox-talk-flags",
      label: "Tool Box Talk Flags",
      icon: <AlertCircle className="h-4 w-4" />,
      content: (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search flags..."
                className="pl-8"
              />
            </div>
            <Button className="flex items-center gap-1">
              <Plus className="h-4 w-4" /> New Flag
            </Button>
          </div>

          <ExpandableDataTable
            data={mockToolBoxTalkFlags}
            columns={[
              { key: 'id', header: 'Flag ID' },
              { key: 'tbtId', header: 'TBT ID' },
              { key: 'issue', header: 'Issue' },
              { key: 'raisedBy', header: 'Raised By' },
              { key: 'raisedDate', header: 'Date Raised' },
              {
                key: 'status',
                header: 'Status',
                render: (value) => {
                  let badgeClass = '';
                  switch(value) {
                    case 'New': badgeClass = 'bg-primary hover:bg-primary/90'; break;
                    case 'Under Review': badgeClass = 'bg-warning-400 hover:bg-warning-500 text-black'; break;
                    case 'Addressed': badgeClass = 'bg-success-500 hover:bg-success-600'; break;
                    case 'Closed': badgeClass = 'bg-muted-foreground hover:bg-gray-600'; break;
                    default: badgeClass = 'bg-muted hover:bg-muted/90';
                  }
                  return <Badge className={badgeClass}>{value}</Badge>;
                }
              },
              {
                key: 'priority',
                header: 'Priority',
                render: (value) => {
                  let badgeClass = '';
                  switch(value) {
                    case 'Low': badgeClass = 'bg-success-500 hover:bg-success-600'; break;
                    case 'Medium': badgeClass = 'bg-warning-400 hover:bg-warning-500 text-black'; break;
                    case 'High': badgeClass = 'bg-destructive hover:bg-destructive/90'; break;
                    default: badgeClass = 'bg-muted hover:bg-muted/90';
                  }
                  return <Badge className={badgeClass}>{value}</Badge>;
                }
              },
            ]}
            onRowClick={(row) => handleRowClick(row.id, 'Tool Box Talk Flag')}
            highlightOnHover={true}
            striped={true}
            showActions={true}
            onEdit={(item) => toast({ title: "Edit Flag", description: `Editing flag ${item.id}` })}
          />
        </div>
      )
    }
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <PageHeader
          title="Risk Assessment"
          description="Identify, analyze, and mitigate potential risks across your organization"
        />
      </div>

      <TabsContainer
        tabs={tabs}
        defaultValue="my-actions"
        onValueChange={(value) => setActiveTab(value)}
      />
    </div>
  );
};

export default RiskAssessmentPage;
