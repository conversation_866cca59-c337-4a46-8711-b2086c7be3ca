import { store } from '../store';
import { setLogout } from '../store/slices/authSlice';

/**
 * Enhanced fetch wrapper that handles authentication and 401 responses
 * Use this for API calls that can't use the axios instance (e.g., external APIs)
 */
export const fetchWithAuth = async (
  url: string,
  options: RequestInit = {}
): Promise<Response> => {
  // Get token from localStorage
  const token = localStorage.getItem('access_token');

  // Prepare headers
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  // Add authorization header if token exists
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  // Make the request
  const response = await fetch(url, {
    ...options,
    headers,
  });

  // Handle 401 responses
  if (response.status === 401) {
    console.log('401 Unauthorized - Logging out user');

    try {
      // Dispatch logout action to update Redux state
      store.dispatch(setLogout());

      // Clear all auth-related data from localStorage
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('headerLogoUrl');

      // Only redirect if we're not already on the login page
      if (!window.location.pathname.includes('/login')) {
        window.location.replace('/login');
      }
    } catch (logoutError) {
      console.error('Error during logout process:', logoutError);
    }

    throw new Error('Unauthorized - User logged out');
  }

  return response;
};

/**
 * Convenience wrapper for GET requests with JSON response
 */
export const fetchJsonWithAuth = async <T = any>(
  url: string,
  options: RequestInit = {}
): Promise<T> => {
  const response = await fetchWithAuth(url, {
    method: 'GET',
    ...options,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
};

/**
 * Convenience wrapper for POST requests with JSON response
 */
export const postJsonWithAuth = async <T = any>(
  url: string,
  data?: any,
  options: RequestInit = {}
): Promise<T> => {
  const response = await fetchWithAuth(url, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
    ...options,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
};

/**
 * Convenience wrapper for PUT requests with JSON response
 */
export const putJsonWithAuth = async <T = any>(
  url: string,
  data?: any,
  options: RequestInit = {}
): Promise<T> => {
  const response = await fetchWithAuth(url, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
    ...options,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
};

/**
 * Convenience wrapper for DELETE requests
 */
export const deleteWithAuth = async (
  url: string,
  options: RequestInit = {}
): Promise<Response> => {
  return await fetchWithAuth(url, {
    method: 'DELETE',
    ...options,
  });
};
