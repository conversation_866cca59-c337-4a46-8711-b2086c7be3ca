import React, { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Button } from 'primereact/button';
import { Dialog } from 'primereact/dialog';
import { DropzoneArea } from 'material-ui-dropzone';
import Select from 'react-select';
import SignatureCanvas from "react-signature-canvas";
import Swal from 'sweetalert2';
import { Modal } from 'react-bootstrap';

// Import the components you provided
import SubActivityComponent from './Component/SubActivityComponent';
import HeadStepper from './Component/HeadStepper';

// Import API and constants (you'll need to provide these)
import API from '../../services/API';
import {
  FILE_URL
} from '../../constants';

// Types
type RiskAssessmentType = 'Routine' | 'Non-Routine' | 'High Risk';

interface SubActivity {
  id: string;
  name: string;
  attachments: File[];
  hazards: HazardItem[];
  consequences: ConsequenceItem[];
  currentControls: string;
  riskEstimation: {
    likelihood: string;
    severity: string;
    riskScore: string;
  };
  expanded: boolean;
  saved: boolean;
  activeSection: 'hazards' | 'consequences' | 'currentControls' | 'riskEstimation';
  sectionsCompleted: {
    hazards: boolean;
    consequences: boolean;
    currentControls: boolean;
    riskEstimation: boolean;
  };
  index: number; // For tracking position in the list for drag and drop
  isFinalized?: boolean; // Whether the activity is finalized and can't be edited
}

// Mock data for dropdowns
const DEPARTMENTS = [
  { label: 'Operations', value: 'operations' },
  { label: 'Maintenance', value: 'maintenance' },
  { label: 'Safety', value: 'safety' },
  { label: 'Quality Control', value: 'quality-control' },
  { label: 'Logistics', value: 'logistics' },
];

const WORK_ACTIVITIES = [
  { label: 'Chemical Handling', value: 'chemical-handling' },
  { label: 'Working at Heights', value: 'working-at-heights' },
  { label: 'Confined Space Entry', value: 'confined-space-entry' },
  { label: 'Electrical Work', value: 'electrical-work' },
  { label: 'Hot Work', value: 'hot-work' },
  { label: 'Lifting Operations', value: 'lifting-operations' },
];

const TEAM_MEMBERS = [
  { label: 'John Smith', value: 'john-smith' },
  { label: 'Sarah Johnson', value: 'sarah-johnson' },
  { label: 'Michael Brown', value: 'michael-brown' },
  { label: 'Emily Davis', value: 'emily-davis' },
  { label: 'Robert Wilson', value: 'robert-wilson' },
];

const RECOMMENDATION_LEVELS = [
  { label: 'Low Risk', value: 'low-risk' },
  { label: 'Medium Risk', value: 'medium-risk' },
  { label: 'High Risk', value: 'high-risk' },
];

const CONTROL_MEASURES = [
  { label: 'Engineering Controls', value: 'engineering-controls' },
  { label: 'Administrative Controls', value: 'administrative-controls' },
  { label: 'PPE', value: 'ppe' },
  { label: 'Elimination', value: 'elimination' },
  { label: 'Substitution', value: 'substitution' },
];

const WORK_TYPES = [
  { label: 'Work at Height', value: 'work-at-height' },
  { label: 'Working in Confined Space', value: 'working-in-confined-space' },
  { label: 'Lifting Operations', value: 'lifting-operations' },
  { label: 'Electrical Work', value: 'electrical-work' },
  { label: 'Hot Work', value: 'hot-work' },
  { label: 'Chemical Handling', value: 'chemical-handling' },
  { label: 'Others', value: 'others' },
];

const LIKELIHOOD_OPTIONS = [
  { label: 'Rare', value: 'rare' },
  { label: 'Unlikely', value: 'unlikely' },
  { label: 'Possible', value: 'possible' },
  { label: 'Likely', value: 'likely' },
  { label: 'Almost Certain', value: 'almost-certain' },
];

const SEVERITY_OPTIONS = [
  { label: 'Negligible', value: 'negligible' },
  { label: 'Minor', value: 'minor' },
  { label: 'Moderate', value: 'moderate' },
  { label: 'Major', value: 'major' },
  { label: 'Catastrophic', value: 'catastrophic' },
];

const RISK_SCORE_OPTIONS = [
  { label: 'Low', value: 'low' },
  { label: 'Medium', value: 'medium' },
  { label: 'High', value: 'high' },
  { label: 'Critical', value: 'critical' },
];

// Form schema
const formSchema = z.object({
  assessmentType: z.enum(['Routine', 'Non-Routine', 'High Risk']),
  department: z.string().min(1, "Department is required"),
  workActivity: z.string().min(1, "Work Activity is required"),
  permitShortName: z.string().min(1, "Permit Short Name is required"),
  teamMembers: z.array(z.string()).min(1, "At least one team member is required"),
  additionalInfo: z.string().optional(),
  recommendationLevel: z.string().min(1, "Recommendation Level is required"),
  controlMeasures: z.string().min(1, "Control Measures is required"),
  workTypes: z.array(z.string()).min(1, "At least one work type is required"),
  additionalRecommendation: z.string().optional(),
  declaration: z.boolean().refine(val => val === true, {
    message: "You must agree to the declaration",
  }),
  signature: z.string().min(1, "Signature is required"),
});

type FormValues = z.infer<typeof formSchema>;

interface AddRiskAssessmentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: any, isDraft: boolean) => void;
}

const AddRiskAssessmentModal: React.FC<AddRiskAssessmentModalProps> = ({
  open,
  onOpenChange,
  onSubmit
}) => {
  // State for multi-step form
  const [currentStep, setCurrentStep] = useState(1);
  const [subActivities, setSubActivities] = useState<SubActivity[]>([]);

  // Form initialization
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      assessmentType: 'Routine',
      department: '',
      workActivity: '',
      permitShortName: '',
      teamMembers: [],
      additionalInfo: '',
      recommendationLevel: '',
      controlMeasures: '',
      workTypes: [],
      additionalRecommendation: '',
      declaration: false,
      signature: '',
    }
  });

  // Handle form submission
  const handleSubmit = (values: FormValues, isDraft: boolean) => {
    const formData = {
      ...values,
      id: `RA-${uuidv4().substring(0, 8)}`,
      subActivities: subActivities,
      status: isDraft ? 'Draft' : 'Pending',
      createdBy: 'Current User',
      createdDate: format(new Date(), 'yyyy-MM-dd'),
      lastUpdated: format(new Date(), 'yyyy-MM-dd'),
    };

    onSubmit(formData, isDraft);
    resetForm();
    onOpenChange(false);
  };

  // Reset form and state
  const resetForm = () => {
    form.reset();
    setSubActivities([]);
    setCurrentStep(1);
  };

  // Navigation between steps
  const nextStep = () => {
    if (currentStep < 6) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Sub-activity management
  const addSubActivity = () => {
    setSubActivities([
      ...subActivities,
      {
        id: uuidv4(),
        name: '',
        attachments: [],
        hazards: [],
        consequences: [],
        currentControls: '',
        riskEstimation: {
          likelihood: '',
          severity: '',
          riskScore: ''
        },
        expanded: false,
        saved: false,
        activeSection: 'hazards',
        sectionsCompleted: {
          hazards: false,
          consequences: false,
          currentControls: false,
          riskEstimation: false
        },
        index: subActivities.length, // Set the index to the current length of the array
        isFinalized: false
      }
    ]);
  };

  const updateSubActivity = (id: string, name: string) => {
    setSubActivities(
      subActivities.map(activity =>
        activity.id === id ? { ...activity, name } : activity
      )
    );
  };

  const toggleSubActivityExpansion = (id: string) => {
    setSubActivities(
      subActivities.map(activity =>
        activity.id === id ? { ...activity, expanded: !activity.expanded } : activity
      )
    );
  };

  const setSubActivitySection = (id: string, section: 'hazards' | 'consequences' | 'currentControls' | 'riskEstimation') => {
    setSubActivities(
      subActivities.map(activity =>
        activity.id === id ? { ...activity, activeSection: section } : activity
      )
    );
  };

  const updateSubActivityHazards = (id: string, hazards: HazardItem[]) => {
    setSubActivities(
      subActivities.map(activity =>
        activity.id === id ? { ...activity, hazards } : activity
      )
    );
  };

  const updateSubActivityConsequences = (id: string, consequences: ConsequenceItem[]) => {
    setSubActivities(
      subActivities.map(activity =>
        activity.id === id ? {
          ...activity,
          consequences,
          sectionsCompleted: {
            ...activity.sectionsCompleted,
            consequences: consequences.length > 0 && consequences.every(c => c.impactOn && c.description)
          }
        } : activity
      )
    );
  };

  const updateSubActivityCurrentControls = (id: string, currentControls: string) => {
    setSubActivities(
      subActivities.map(activity =>
        activity.id === id ? { ...activity, currentControls } : activity
      )
    );
  };

  const updateSubActivityRiskEstimation = (id: string, field: 'likelihood' | 'severity' | 'riskScore', value: string) => {
    setSubActivities(
      subActivities.map(activity =>
        activity.id === id ? {
          ...activity,
          riskEstimation: {
            ...activity.riskEstimation,
            [field]: value
          }
        } : activity
      )
    );
  };

  // Save a sub-activity
  const saveSubActivity = (id: string) => {
    setSubActivities(
      subActivities.map(activity =>
        activity.id === id ? {
          ...activity,
          saved: true,
          expanded: true,
          activeSection: 'hazards' // Set default active section to hazards
        } : activity
      )
    );
  };

  // Mark a section as completed
  const markSectionCompleted = (id: string, section: 'hazards' | 'consequences' | 'currentControls' | 'riskEstimation') => {
    setSubActivities(
      subActivities.map(activity =>
        activity.id === id ? {
          ...activity,
          sectionsCompleted: {
            ...activity.sectionsCompleted,
            [section]: true
          }
        } : activity
      )
    );
  };

  const handleFileUpload = (id: string, files: FileList) => {
    setSubActivities(
      subActivities.map(activity =>
        activity.id === id
          ? { ...activity, attachments: [...activity.attachments, ...Array.from(files)] }
          : activity
      )
    );
  };

  const removeSubActivity = (id: string) => {
    setSubActivities(subActivities.filter(activity => activity.id !== id));
  };

  const removeAttachment = (activityId: string, fileIndex: number) => {
    setSubActivities(
      subActivities.map(activity =>
        activity.id === activityId
          ? {
              ...activity,
              attachments: activity.attachments.filter((_, index) => index !== fileIndex)
            }
          : activity
      )
    );
  };

  // Handle drag and drop reordering
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(subActivities);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update indices after reordering
    const updatedItems = items.map((item, index) => ({
      ...item,
      index
    }));

    setSubActivities(updatedItems);
  };

  return (
    <Dialog
      visible={open}
      onHide={() => onOpenChange(false)}
      style={{ width: '95vw', maxWidth: '1400px', height: '90vh' }}
      header="Add New Risk Assessment"
      modal
      className="p-fluid"
    >
      <div className="row" style={{ height: '100%', overflow: 'auto' }}>
        <div className="col-12">
          <div className="card">
            <div className='card-body p-0'>
              {/* Introduction Section */}
              <div className=' p-4 pt-0'>
                {type === 'routine' ? (
                  <>
                    <p className='fst-italic'>A risk evaluation conducted for activities that are regularly performed and require controls in place within the organization's operations.</p>
                    <p className=''>To proceed, select the relevant Department and Work Activity from the drop-down list of pre-configured options. If you cannot find a specific department or work activity, check the Risk Register, as a risk assessment may have already been completed for that activity. If it is not listed, please contact the Enterprise Administrator to have it added.</p>
                    <p className=''>If the work activity is not routinely conducted by the organization and is therefore not found in the work activity register, switch to Non-Routine Risk Assessment and manually enter the title of the work to continue.</p>
                  </>
                ) : (
                  <>
                    <p className='fst-italic '>An evaluation focused on activities that are not regularly performed and do not have established controls, requiring a detailed assessment to ensure appropriate safeguards are identified and implemented.</p>
                  </>
                )}
              </div>

              {/* Department and Activity Selection */}
              <div className='borderSection p-4'>
                {type === 'routine' ? (
                  <div className='row mb-4'>
                    <div className='col-4'>
                      <div className='mb-2'>Choose Operational Risk Area...</div>
                      <Select
                        labelKey="name"
                        id="user_description"
                        onChange={(e: any) => { setSelectedDepart(e); setSelectedActivity(null) }}
                        options={depart}
                        placeholder="Operational Risk Area"
                        value={selectedDepart}
                      />
                    </div>
                    <div className='col-8'>
                      <div className='mb-2'>Choose Work Activity...</div>
                      <Select
                        labelKey="name"
                        id="user_description"
                        onChange={(e: any) => setSelectedActivity(e)}
                        options={activity}
                        placeholder="WorkActivity"
                        value={selectedActivity}
                      />
                    </div>
                  </div>
                ) : type === 'nonroutine' ? (
                  <div className='row mb-4'>
                    <div className='col-12'>
                      <div className='mb-2'>To proceed, enter the title of the work to continue.</div>
                      <InputTextarea
                        autoResize
                        rows={3}
                        placeholder='Work Activity'
                        value={nonRoutineActivity}
                        onChange={(e) => setNonRoutineActivity(e.target.value)}
                        style={{ width: '100%' }}
                      />
                    </div>
                  </div>
                ) : (
                  <div className='row mb-4'>
                    <div className='col-8'>
                      <InputText
                        placeholder='Hazard Name'
                        onChange={(e) => setHazardName(e.target.value)}
                        style={{ width: '100%' }}
                      />
                    </div>
                  </div>
                )}

                {/* Team Members Selection */}
                <div className='row mb-4'>
                  <div className='col-12'>
                    <div className='mb-2'>Identify the qualified RA Team Members to include in this Risk Assessment using the drop-down selector; only qualified members will be listed. If a required member is not listed, please contact the Administrator to have them added. Once you've made your selections, click the Send Notification button to notify them via email about their inclusion in the team.</div>
                    <Select
                      labelKey="name"
                      valueKey="id"
                      id="user_description"
                      onChange={(e: any) => setSelectedCrew(e)}
                      options={crew}
                      isMulti={true}
                      value={selectedCrew}
                      getOptionLabel={(option: any) => option.name}
                      getOptionValue={(option: any) => option.id}
                      placeholder="Choose Members.."
                    />
                  </div>
                </div>
              </div>

              {/* Additional Information Section */}
              <div className='borderSection p-4'>
                <div className='row mb-4'>
                  <div className="d-flex flex-column col-12">
                    <label htmlFor="username" className='mb-2'>Provide additional information about the work activity to clarify scope (Optional)</label>
                    <InputTextarea
                      rows={3}
                      autoResize
                      cols={30}
                      value={activityDesc}
                      onChange={(e) => setActivityDesc(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              {/* Sub Activities Section */}
              <div className='borderSection p-4'>
                <div className='row mb-4'>
                  <div className='col-12'>
                    <h4 className="risk-title-sub mb-3">Sub Activities</h4>
                    <div className='mb-2'>Identify and list all sub-activities associated with the selected process. For each sub-activity, provide a clear description and, where feasible, upload any relevant images. These images will be utilized in the risk communication modules to enhance understanding and awareness of the process and associated risks.</div>
                    <div className='mb-2'>Click on each listed sub-activity and follow the provided guidance to identify the hazards, potential consequences, current controls, and assess the risks specific to that sub-activity.</div>
                  </div>
                </div>

                <div className='col-12 mb-4'>
                  {task.length === 0 ? (
                    <p>No sub-activities added</p>
                  ) : (
                    task.map((item, i) => (
                      <div key={i} className="card mb-3">
                        <div className="card-body">
                          <div className="d-flex justify-content-between align-items-center">
                            <h5 className="card-title">{item[0].name}</h5>
                            <div>
                              <Button
                                label="Edit"
                                className="p-button-sm p-button-outlined me-2"
                                onClick={() => openDialog(item, i)}
                              />
                              <Button
                                label="Delete"
                                className="p-button-sm p-button-danger"
                                onClick={(e) => deleteTask(e, i)}
                              />
                            </div>
                          </div>
                          {item[0].images && item[0].images.length > 0 && (
                            <div className="mt-2">
                              <small className="text-muted">{item[0].images.length} image(s) attached</small>
                            </div>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                </div>

                <div className='row'>
                  <div className='col-4'>
                    <Button
                      label="Add Sub-Activity"
                      outlined
                      className='d-flex'
                      onClick={() => setAddSubActivity(!addSubActivity)}
                    />
                  </div>
                </div>

                {addSubActivity && (
                  <div className='p-4 mt-4' style={{ border: '1px solid rgba(209, 213, 219, 1)' }}>
                    <div className='col-12'>
                      <div className="d-flex flex-column col-12">
                        <label htmlFor="username" className='mb-2'>Sub-Activity Name</label>
                        <InputText
                          value={subActivityName}
                          onChange={(e) => setSubActivityName(e.target.value)}
                        />
                      </div>
                    </div>
                    <div className='col-12 mt-3'>
                      <label htmlFor="username" className='mb-2'>Image Uploads</label>
                      <div className="mb-3">
                        <DropzoneArea
                          acceptedFiles={['image/jpeg', 'image/png']}
                          dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                          filesLimit={5}
                          maxFileSize={104857600}
                          onChange={(files) => handleFileChange(files)}
                          showPreviewsInDropzone={false}
                          showPreviews={true}
                          dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center '}
                        />
                      </div>
                    </div>
                    <div className='col-12 mt-3'>
                      <Button label="Save" onClick={() => AddSubActivityTitle()} />
                    </div>
                  </div>
                )}
              </div>

              {/* Overall Recommendations Section */}
              <div className='borderSection p-4'>
                <h5 className="mb-4 fw-bold">Overall recommendations of the RA Team</h5>
                <div className='row mb-4'>
                  <div className='col-12'>
                    <div className='row'>
                      <div className='col-12 mb-3 d-flex align-items-start'>
                        <div className="number-circle me-3">1</div>
                        <div className='flex-grow-1'>
                          <Select
                            labelKey="label"
                            id="user_description"
                            value={recommendationOne}
                            onChange={(e: any) => setRecommendationOne(e)}
                            options={[
                              { label: 'The overall risk level for this work activity is LOW. No further additional controls actions are necessary. Monitoring is required to ensure that the controls are maintained.', value: '0' },
                              { label: 'The overall risk level for this work activity is MEDIUM. Work can progress with close supervision and monitoring of current controls. Additional controls identified should be implemented within the defined period of time.', value: '1' },
                              { label: 'The overall risk level for this work activity is HIGH. Work should not be started or continued until the risk level has been reduced and risk numbers enters the LOW or MEDIUM zone.', value: '2' }
                            ]}
                            placeholder="Choose ..."
                          />
                        </div>
                      </div>

                      <div className='col-12 mb-3 d-flex align-items-start'>
                        <div className="number-circle me-3">2</div>
                        <div className='flex-grow-1'>
                          <Select
                            labelKey="label"
                            id="user_description"
                            value={recommendationTwo}
                            onChange={(e: any) => setRecommendationTwo(e)}
                            options={[
                              { label: 'Since this is routine activity with low risk, no formal permit is recommended. However, a dynamic review is advised in case of changing circumstances.', value: '0' },
                              { label: 'A formal permit to work is required before work can commence. Duration will be specified by the permit approver.', value: '1' },
                              { label: 'A formal permit to work that is required. This is valid for a specific task and limited duration. The permit shall include the names of individuals involved in the work.', value: '2' }
                            ]}
                            placeholder="Choose ..."
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className='row mb-4'>
                  <div className='col-12 d-flex align-items-start'>
                    <div className="number-circle me-3">3</div>
                    <div className='flex-grow-1'>
                      <div className='col-12' style={{ border: '1px solid #ced4da' }}>
                        <p className='p-2'>Considering the hazards and risks associated with this work activity, the RA team requires the following high-risk permits to be approved and active when applying for a permit for this specific activity:</p>
                        <div className="d-flex col-12 p-3">
                          {risk.length !== 0 &&
                            risk.map((item, index) => (
                              <label key={index} className='label-role checkbox-bootstrap checkbox-lg col-4 me-3'>
                                <input
                                  value={item.hazardName}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setEptwHot((prev) => [...prev, item]);
                                    } else {
                                      setEptwHot(prevData => prevData.filter(item1 => item1.id !== item.id));
                                    }
                                  }}
                                  checked={eptwHot && eptwHot.some(item1 => item1.id === item.id)}
                                  type='checkbox'
                                  className='me-1'
                                />
                                <span className="checkbox-placeholder"></span>
                                {item.hazardName}
                              </label>
                            ))
                          }
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className='row mb-4'>
                  <div className='col-12 d-flex align-items-start'>
                    <div className="number-circle me-3">4</div>
                    <div className='flex-grow-1'>
                      <div className="d-flex flex-column col-12">
                        <label htmlFor="username" className='mb-2'>Additional Recommendation</label>
                        <InputTextarea
                          autoResize
                          rows={3}
                          cols={30}
                          value={additionalRecommendation}
                          onChange={(e) => setAdditionalRecommendation(e.target.value)}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Declaration Section */}
              <div className='borderSection p-4'>
                <h5 className="mb-4 fw-bold">Declaration</h5>
                <div className='row mb-2'>
                  {type === 'routine' ? (
                    <p>As the Team Leader for this Routine Risk Assessment, I affirm my role in guiding the identification of potential risks and necessary controls. The controls listed have been evaluated by the team based on their professional expertise and are essential for ensuring safety in this activity. This outcome reflects the collective judgment of the team, reached through collaboration and consensus.</p>
                  ) : type === 'nonroutine' ? (
                    <p>As the Team Leader for this Non-Routine Risk Assessment, I confirm my responsibility in guiding the team through the identification of potential risks and necessary controls for activities that are not part of the organization's routine work activity inventory. Given that these activities are not regularly undertaken, we are placing additional focus on documenting the risks and ensuring that appropriate controls are in place. The controls identified are essential for maintaining safety, and this conclusion has been reached through consensus based on the team's collective professional judgment and experience.</p>
                  ) : (
                    <p>As the Team Leader for this High-Risk Hazard Assessment, I confirm my responsibility in guiding the team through the identification of potential risks and necessary controls for high-risk activities. The controls identified are essential for maintaining safety, and this conclusion has been reached through consensus based on the team's collective professional judgment and experience.</p>
                  )}
                </div>

                <div className='row mb-4 text-center'>
                  <div className="d-flex flex-column col-12">
                    <div className="row mt-4">
                      <div className="col-12">
                        <SignatureCanvas
                          penColor="#1F3BB3"
                          canvasProps={{
                            width: 450,
                            height: 120,
                            className: "sigCanvas",
                            style: {
                              boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                            },
                          }}
                          ref={signRef}
                        />
                        <i className="fa fa-undo undo" onClick={() => signRef.current?.clear()}></i>
                        <p>{user?.firstName || 'Team Leader'}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="col-12 text-center" style={{ padding: 20 }}>
                <button
                  style={{ marginRight: 10 }}
                  type="button"
                  className="btn btn-primary mb-3"
                  onClick={(e) => {
                    e.preventDefault();
                    // Handle draft save
                    onSubmit({
                      type,
                      task,
                      selectedDepart,
                      selectedActivity,
                      selectedCrew,
                      activityDesc,
                      recommendationOne,
                      recommendationTwo,
                      additionalRecommendation,
                      eptwHot,
                      nonRoutineDepartment,
                      nonRoutineActivity,
                      hazardName,
                      shortName
                    }, true);
                  }}
                >
                  Save as Draft
                </button>
                <button
                  type="button"
                  className="btn btn-secondary mb-3"
                  onClick={(e) => {
                    e.preventDefault();
                    // Handle final submission
                    onSubmit({
                      type,
                      task,
                      selectedDepart,
                      selectedActivity,
                      selectedCrew,
                      activityDesc,
                      recommendationOne,
                      recommendationTwo,
                      additionalRecommendation,
                      eptwHot,
                      nonRoutineDepartment,
                      nonRoutineActivity,
                      hazardName,
                      shortName
                    }, false);
                  }}
                >
                  Release Draft for Affirmation
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Sub-Activity Modal */}
      {item !== '' && (
        <Modal show={visible} onHide={() => setVisible(false)} size='lg'>
          <Modal.Header closeButton>
            <Modal.Title>Sub-activity Risk Assessment</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <SubActivityComponent
              item={item}
              onSave={editSubActivityTitle}
            />

            <hr />
            {item !== '' && item[8] && item[8].step !== undefined && (
              <HeadStepper
                activeStage={item[8].step}
                stages={item[9].level}
                stageStatus={item[11].value}
                handleStageClick={handleStageClick}
                getStatusClass={getStatusClass}
              />
            )}
            <hr />

            {/* Step content based on active step */}
            {item[8].step === 0 && (
              <div>
                <p>For this sub-activity, identify associated hazards by selecting from the various hazard types displayed. Each type will feature icons representing specific hazards. Once selected, these hazards will automatically appear in other modules, such as Risk Communication, Permit to Work, and Toolbox Talks</p>
                <p>If you encounter a specific hazard that is not included in the library, please send an <NAME_EMAIL> so that it can be promptly added to the hazard library.</p>
                <div className="p-4 border rounded">
                  <p className="text-muted">Hazards Identification Panel - Component to be implemented</p>
                </div>
              </div>
            )}

            {item[8].step === 1 && (
              <div>
                <h6 className='fw-bold'>Consequences</h6>
                <p>For this sub-activity, list and elaborate on all potential consequences associated with the identified hazards, covering impacts on Personnel, Environment, Property/Equipment, and Operations, as applicable. Include all relevant areas that apply to the specific hazards of the sub-activity.</p>
                <div className="p-4 border rounded">
                  <p className="text-muted">Consequences Panel - Component to be implemented</p>
                </div>
              </div>
            )}

            {item[8].step === 2 && (
              <div>
                <h6 className='fw-bold'>Current Controls</h6>
                <p>Identify and describe in detail the current controls in place to manage the hazards and minimize their consequences. Current controls refer to existing safety measures, procedures, and other implemented actions to reduce risks associated with the sub-activity.</p>
                <div className="p-4 border rounded">
                  <p className="text-muted">Current Controls Panel - Component to be implemented</p>
                </div>
              </div>
            )}

            {item[8].step === 3 && (
              <div>
                <h6 className='fw-bold'>Risk Assessment</h6>
                <p>Assess the risk level by evaluating the likelihood and severity of potential consequences.</p>
                <div className="p-4 border rounded">
                  <p className="text-muted">Risk Assessment Panel - Component to be implemented</p>
                </div>
              </div>
            )}

            {item[8].step === 4 && (
              <div>
                <h6 className='fw-bold'>Additional Controls</h6>
                <p>Identify additional risk management measures that should be implemented to further reduce risks.</p>
                <div className="p-4 border rounded">
                  <p className="text-muted">Additional Controls Panel - Component to be implemented</p>
                </div>
              </div>
            )}
          </Modal.Body>

          <Modal.Footer>
            <div className="d-flex justify-content-between align-items-center w-100">
              <div className="d-flex">
                <Button
                  className='me-2'
                  outlined
                  label={`Save Progress`}
                  onClick={saveProgress}
                />
                <Button
                  label={`Save & Finalize ${sectionNames[item[8].step]} for Sub Activity`}
                  onClick={handleNext}
                />
              </div>
            </div>
          </Modal.Footer>
        </Modal>
      )}
    </Dialog>
  );
};

export default AddRiskAssessmentModal;
