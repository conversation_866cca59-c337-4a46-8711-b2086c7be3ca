import React, { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import SignatureCan<PERSON> from "react-signature-canvas";
import Swal from 'sweetalert2';

// Import API and constants
import API from '../../services/API';
import {
  FILE_URL,
  HAZARDS_CATEGOTY,
  GET_ALL_USER,
  RISKASSESSMENT_LIST,
  GET_RISK_HAZARD_URL
} from '../../constants';

// Types based on test.js structure
interface TaskItem {
  type: string;
  name?: string;
  images?: string[];
  selected?: any[];
  option?: any[];
  severity?: string;
  likelyhood?: string;
  level?: string;
  accept?: boolean;
  step?: number;
  value?: any;
  person?: string;
  date?: Date | null;
  current_type?: string;
  method?: string;
  files?: string[];
  required?: boolean;
  validity?: boolean;
}

// Custom UI Components
const CustomInput: React.FC<{
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  type?: string;
}> = ({ value, onChange, placeholder, className = '', type = 'text' }) => (
  <input
    type={type}
    value={value}
    onChange={(e) => onChange(e.target.value)}
    placeholder={placeholder}
    className={`custom-input ${className}`}
  />
);

const CustomTextarea: React.FC<{
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  className?: string;
}> = ({ value, onChange, placeholder, rows = 3, className = '' }) => (
  <textarea
    value={value}
    onChange={(e) => onChange(e.target.value)}
    placeholder={placeholder}
    rows={rows}
    className={`custom-textarea ${className}`}
  />
);

const CustomButton: React.FC<{
  children: React.ReactNode;
  onClick: () => void;
  variant?: 'primary' | 'secondary' | 'danger' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
}> = ({ children, onClick, variant = 'primary', size = 'md', disabled = false, className = '' }) => (
  <button
    onClick={onClick}
    disabled={disabled}
    className={`custom-btn custom-btn-${variant} custom-btn-${size} ${className}`}
  >
    {children}
  </button>
);

const CustomSelect: React.FC<{
  options: { label: string; value: string }[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}> = ({ options, value, onChange, placeholder, className = '' }) => (
  <select
    value={value}
    onChange={(e) => onChange(e.target.value)}
    className={`custom-select ${className}`}
  >
    {placeholder && <option value="">{placeholder}</option>}
    {options.map((option) => (
      <option key={option.value} value={option.value}>
        {option.label}
      </option>
    ))}
  </select>
);

const CustomMultiSelect: React.FC<{
  options: { label: string; value: string; id?: string; name?: string }[];
  value: any[];
  onChange: (value: any[]) => void;
  placeholder?: string;
  className?: string;
}> = ({ options, value, onChange, placeholder, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleToggle = (option: any) => {
    const isSelected = value.some(v => v.id === option.id || v.value === option.value);
    if (isSelected) {
      onChange(value.filter(v => v.id !== option.id && v.value !== option.value));
    } else {
      onChange([...value, option]);
    }
  };

  return (
    <div className={`custom-multiselect ${className}`}>
      <div className="multiselect-trigger" onClick={() => setIsOpen(!isOpen)}>
        <span>
          {value.length > 0
            ? `${value.length} selected`
            : placeholder || 'Select options...'}
        </span>
        <i className={`arrow ${isOpen ? 'up' : 'down'}`}>▼</i>
      </div>
      {isOpen && (
        <div className="multiselect-dropdown">
          {options.map((option) => {
            const isSelected = value.some(v => v.id === option.id || v.value === option.value);
            return (
              <div
                key={option.id || option.value}
                className={`multiselect-option ${isSelected ? 'selected' : ''}`}
                onClick={() => handleToggle(option)}
              >
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={() => {}}
                />
                <span>{option.name || option.label}</span>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

const CustomModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}> = ({ isOpen, onClose, title, children, size = 'lg' }) => {
  if (!isOpen) return null;

  return (
    <div className="custom-modal-overlay" onClick={onClose}>
      <div
        className={`custom-modal custom-modal-${size}`}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="modal-header">
          <h3>{title}</h3>
          <button className="modal-close" onClick={onClose}>×</button>
        </div>
        <div className="modal-body">
          {children}
        </div>
      </div>
    </div>
  );
};

const CustomCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  title?: string;
}> = ({ children, className = '', title }) => (
  <div className={`custom-card ${className}`}>
    {title && <div className="card-header">{title}</div>}
    <div className="card-body">{children}</div>
  </div>
);

const CustomTabs: React.FC<{
  tabs: { label: string; content: React.ReactNode }[];
  activeTab: number;
  onTabChange: (index: number) => void;
}> = ({ tabs, activeTab, onTabChange }) => (
  <div className="custom-tabs">
    <div className="tabs-header">
      {tabs.map((tab, index) => (
        <button
          key={index}
          className={`tab-button ${activeTab === index ? 'active' : ''}`}
          onClick={() => onTabChange(index)}
        >
          {tab.label}
        </button>
      ))}
    </div>
    <div className="tabs-content">
      {tabs[activeTab]?.content}
    </div>
  </div>
);

const CustomStepper: React.FC<{
  steps: string[];
  activeStep: number;
  onStepClick: (step: number) => void;
  completedSteps: number[];
}> = ({ steps, activeStep, onStepClick, completedSteps }) => (
  <div className="custom-stepper">
    {steps.map((step, index) => (
      <div key={index} className="stepper-item">
        <div
          className={`stepper-circle ${
            completedSteps.includes(index) ? 'completed' :
            activeStep === index ? 'active' : 'pending'
          }`}
          onClick={() => onStepClick(index)}
        >
          {completedSteps.includes(index) ? '✓' : index + 1}
        </div>
        <div className="stepper-label">{step}</div>
        {index < steps.length - 1 && (
          <div className={`stepper-line ${completedSteps.includes(index) ? 'completed' : ''}`} />
        )}
      </div>
    ))}
  </div>
);

const CustomFileUpload: React.FC<{
  onFileSelect: (files: File[]) => void;
  accept?: string;
  multiple?: boolean;
  maxFiles?: number;
}> = ({ onFileSelect, accept = "image/*", multiple = true, maxFiles = 5 }) => {
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    const files = Array.from(e.dataTransfer.files);
    onFileSelect(files.slice(0, maxFiles));
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      onFileSelect(files.slice(0, maxFiles));
    }
  };

  return (
    <div
      className={`custom-file-upload ${dragOver ? 'drag-over' : ''}`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={() => fileInputRef.current?.click()}
    >
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileInput}
        style={{ display: 'none' }}
      />
      <div className="upload-content">
        <div className="upload-icon">📁</div>
        <div className="upload-text">
          <p>Drag & drop files here or click to browse</p>
          <small>Maximum {maxFiles} files allowed</small>
        </div>
      </div>
    </div>
  );
};

const CustomRadioGroup: React.FC<{
  options: { label: string; value: any }[];
  value: any;
  onChange: (value: any) => void;
  name: string;
}> = ({ options, value, onChange, name }) => (
  <div className="custom-radio-group">
    {options.map((option, index) => (
      <label key={index} className="radio-option">
        <input
          type="radio"
          name={name}
          value={option.value}
          checked={value === option.value}
          onChange={() => onChange(option.value)}
        />
        <span className="radio-custom"></span>
        <span className="radio-label">{option.label}</span>
      </label>
    ))}
  </div>
);

const CustomCheckbox: React.FC<{
  checked: boolean;
  onChange: (checked: boolean) => void;
  label: string;
  className?: string;
}> = ({ checked, onChange, label, className = '' }) => (
  <label className={`custom-checkbox ${className}`}>
    <input
      type="checkbox"
      checked={checked}
      onChange={(e) => onChange(e.target.checked)}
    />
    <span className="checkbox-custom"></span>
    <span className="checkbox-label">{label}</span>
  </label>
);

const CustomTable: React.FC<{
  data: any[];
  columns: { key: string; label: string; render?: (value: any, row: any) => React.ReactNode }[];
  className?: string;
}> = ({ data, columns, className = '' }) => (
  <div className={`custom-table-container ${className}`}>
    <table className="custom-table">
      <thead>
        <tr>
          {columns.map((column) => (
            <th key={column.key}>{column.label}</th>
          ))}
        </tr>
      </thead>
      <tbody>
        {data.map((row, index) => (
          <tr key={index}>
            {columns.map((column) => (
              <td key={column.key}>
                {column.render ? column.render(row[column.key], row) : row[column.key]}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  </div>
);

// Static data based on test.js structure
const severity = [
  { "value": "1", "label": "1(E) - Negligible" },
  { "value": "2", "label": "2(D) - Minor" },
  { "value": "3", "label": "3(C) - Moderate" },
  { "value": "4", "label": "4(B) - Major" },
  { "value": "5", "label": "5(A) - Catastrophic" }
];

const likelyhood = [
  { label: "Rare (1)", value: "1" },
  { label: "Unlikely (2)", value: "2" },
  { label: "Possible (3)", value: "3" },
  { label: "Likely (4)", value: "4" },
  { label: "Almost Certain (5)", value: "5" },
];

const impactOn = [
  { 'label': 'Personnel', 'value': 'Personnel' },
  { 'label': 'Environment', 'value': 'Environment' },
  { 'label': 'Property / Equipment', 'value': 'Property / Equipment' },
  { 'label': 'Operations', 'value': 'Operations' },
];

const control = [
  { 'label': 'No Control', 'value': 'No Control' },
  { 'label': 'Engineering', 'value': 'Engineering' },
  { 'label': 'Administrative', 'value': 'Administrative' },
  { 'label': 'PPE', 'value': 'PPE' }
];

const controlAdditional = [
  { 'label': 'Elimination', 'value': 'Elimination' },
  { 'label': 'Substitution', 'value': 'Substitution' },
  { 'label': 'Engineering', 'value': 'Engineering' },
  { 'label': 'Administrative', 'value': 'Administrative' },
  { 'label': 'PPE', 'value': 'PPE' }
];

const controlType = [
  { 'label': 'Preventative', 'value': 'Preventative' },
  { 'label': 'Mitigative', 'value': 'Mitigative' }
];

const severityData = [
  {
    id: '5 (A)',
    severity: 'Catastrophic',
    personnel: 'Serious injury with long-term or permanent disability or death.',
    property: 'Significant damage leading to major repairs.',
    environment: 'Significant environmental damage requiring regulatory reporting and cleanup.',
    serviceLoss: 'Major disruption to service operations, extended recovery time.'
  },
  {
    id: '4 (B)',
    severity: 'Major',
    personnel: 'Serious injury with long-term recovery or permanent disability.',
    property: 'Noticeable damage requiring repair, causing moderate downtime or repair costs.',
    environment: 'Moderate, recoverable environmental impact, requiring external agency notification or cleanup.',
    serviceLoss: 'Significant downtime with substantial recovery efforts.'
  },
  {
    id: '3 (C)',
    severity: 'Moderate',
    personnel: 'Injury requiring medical treatment with potential for short-term lost workdays or restricted duties.',
    property: 'Noticeable damage requiring repair, causing moderate downtime or repair costs.',
    environment: 'Moderate environmental impact, manageable on-site with potential regulatory notification.',
    serviceLoss: 'Moderate service interruption with short recovery.'
  },
  {
    id: '2 (D)',
    severity: 'Minor',
    personnel: 'Minor injury requiring first-aid or outpatient treatment, with minimal lost time.',
    property: 'Slight damage requiring minor repairs without significant downtime.',
    environment: 'Small localized impact, manageable on-site, without long-term environmental damage.',
    serviceLoss: 'Brief disruption to services, easily restored.'
  },
  {
    id: '1 (E)',
    severity: 'Insignificant',
    personnel: 'Minor first-aid required with no lost time or long-term health impacts.',
    property: 'Minimal damage or wear that does not require repair or interruption to operations.',
    environment: 'Negligible environmental impact with no regulatory involvement needed.',
    serviceLoss: 'No impact on services.'
  }
];

const levelData = [
  {
    level: '1',
    descriptor: 'Rare',
    detailedDescription: 'The event is highly unlikely to occur under normal circumstances, with little to no historical precedent.'
  },
  {
    level: '2',
    descriptor: 'Unlikely',
    detailedDescription: 'The event is improbable but could potentially happen under unusual conditions, though there is limited historical data to support this.'
  },
  {
    level: '3',
    descriptor: 'Possible',
    detailedDescription: 'The event could happen, with moderate chances of occurring based on historical records or foreseeable conditions.'
  },
  {
    level: '4',
    descriptor: 'Likely',
    detailedDescription: 'The event is expected to occur in the normal course of operations, with a significant history of similar incidents.'
  },
  {
    level: '5',
    descriptor: 'Almost Certain',
    detailedDescription: 'The event is highly likely to occur and is anticipated in the near future without further intervention.'
  }
];

const tableData = [
  { id: '5(A)', severity: 'Catastrophic', rare: '5(A)', unlikely: '10(A)', possible: '15(A)', likely: '20(A)', almostCertain: '25(A)' },
  { id: '4(B)', severity: 'Major', rare: '4(B)', unlikely: '8(B)', possible: '12(B)', likely: '16(B)', almostCertain: '20(B)' },
  { id: '3(C)', severity: 'Moderate', rare: '3(C)', unlikely: '6(C)', possible: '9(C)', likely: '12(C)', almostCertain: '15(C)' },
  { id: '2(D)', severity: 'Minor', rare: '2(D)', unlikely: '4(D)', possible: '6(D)', likely: '8(D)', almostCertain: '10(D)' },
  { id: '1(E)', severity: 'Insignificant', rare: '1(E)', unlikely: '2(E)', possible: '3(E)', likely: '4(E)', almostCertain: '5(E)' },
];

// Utility functions
const getStatusClass = (task: TaskItem[]) => {
  if (!task || !task[11] || !task[11].value) return 'bg-secondary';

  const statuses = Object.values(task[11].value);
  if (statuses.some((status: any) => status === 'completed')) return 'bg-success';
  if (statuses.some((status: any) => status === 'inprogress')) return 'bg-warning';
  return 'bg-secondary';
};

const rowClassName = (data: any) => {
  switch (data.level[0]) {
    case '1': return 'level-1';
    case '2': return 'level-2';
    case '3': return 'level-3';
    case '4': return 'level-4';
    case '5': return 'level-5';
    default: return '';
  }
};

const cellClassName = (value: any) => {
  const numericValue = parseInt(String(value).replace(/[^\d]/g, ''), 10);
  if (numericValue === 0) return '';
  if (numericValue === 1 || numericValue === 2 || numericValue === 3 || numericValue === 4) return 'cell-green';
  if (numericValue === 15 || numericValue === 20 || numericValue === 25 || numericValue === 16) return 'cell-red';
  return 'cell-yellow';
};

const cellStyle = (data: any, field: string) => cellClassName(data[field]);

interface AddRiskAssessmentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: any, isDraft: boolean) => void;
  type: 'routine' | 'nonroutine' | 'highrisk';
}

const AddRiskAssessmentModal: React.FC<AddRiskAssessmentModalProps> = ({
  open,
  onOpenChange,
  onSubmit,
  type = 'routine'
}) => {
  const user = useSelector((state: any) => state.login.user);
  const signRef = useRef<SignatureCanvas>(null);

  // State variables based on test.js structure
  const [files, setFiles] = useState<File[]>([]);
  const [depart, setDepart] = useState<any[]>([]);
  const [activity, setActivity] = useState<any[]>([]);
  const [crew, setCrew] = useState<any[]>([]);
  const [selectedDepart, setSelectedDepart] = useState<any>(null);
  const [selectedActivity, setSelectedActivity] = useState<any>(null);
  const [selectedCrew, setSelectedCrew] = useState<any[]>([]);
  const [addSubActivity, setAddSubActivity] = useState(false);
  const [activityDesc, setActivityDesc] = useState('');
  const [task, setTask] = useState<TaskItem[][]>([]);
  const [subActivityName, setSubActivityName] = useState('');
  const [visible, setVisible] = useState(false);
  const [item, setItem] = useState<TaskItem[] | ''>('');
  const [index, setIndex] = useState<number | ''>('');
  const [hazards, setHazards] = useState<any[]>([]);
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [responsibility, setResponsibility] = useState<any[]>([]);
  const [required, setRequired] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [risk, setRisk] = useState<any[]>([]);
  const [eptwHot, setEptwHot] = useState<any[]>([]);
  const [recommendationOne, setRecommendationOne] = useState<any>(null);
  const [recommendationTwo, setRecommendationTwo] = useState<any>(null);
  const [additionalRecommendation, setAdditionalRecommendation] = useState('');
  const [nonRoutineActivity, setNonRoutineActivity] = useState('');
  const [nonRoutineDepartment, setNonRoutineDepartment] = useState('');
  const [hazardName, setHazardName] = useState('');
  const [shortName, setShortName] = useState('');

  // API functions based on test.js
  const getRoutineList = async () => {
    try {
      const response = await API.get(RISKASSESSMENT_LIST);
      if (response.status === 200) {
        setDepart(response.data);
      }
    } catch (error) {
      console.error('Error fetching routine list:', error);
    }
  };

  const getHazardList = async () => {
    try {
      const selectedIndustry = localStorage.getItem('SELECTED_INDUSTRIES');

      if (selectedIndustry) {
        const selectedIndustryNames = selectedIndustry
          .split(',')
          .map(name => name.trim());

        const response = await API.get(GET_RISK_HAZARD_URL);
        if (response.status === 200) {
          const industryList = response.data;
          const matchedIndustries = industryList.filter((item: any) =>
            selectedIndustryNames.includes(item.name)
          );
          const allHazards = matchedIndustries.flatMap(
            (industry: any) => industry.hazardCategories || []
          );
          setHazards(allHazards);
        }
      } else {
        const response = await API.get(HAZARDS_CATEGOTY);
        if (response.status === 200) {
          const data = response.data.filter(
            (item: any) => item.name !== 'Hazard-Based'
          );
          setHazards(data);
        }
      }
    } catch (error) {
      console.error('Error fetching hazard list:', error);
    }
  };

  const getAllResponsibility = async () => {
    try {
      const response = await API.get(GET_ALL_USER);
      if (response.status === 200) {
        const depart = response.data.map((item: any) => ({
          id: item.id,
          name: item.firstName,
          email: item.email
        }));
        setResponsibility(depart);
      }
    } catch (error) {
      console.error('Error fetching responsibility list:', error);
    }
  };

  const getHighRiskHazardList = async () => {
    try {
      const response = await API.get(RISKASSESSMENT_LIST);
      if (response.status === 200) {
        setRisk(response.data);
      }
    } catch (error) {
      console.error('Error fetching high-risk hazard list:', error);
    }
  };

  // File handling
  const handleFileChange = (files: File[]) => {
    setFiles(files);
  };

  // Sub-activity management based on test.js structure
  const AddSubActivityTitle = async () => {
    const image: string[] = [];

    if (files.length > 0) {
      for (const file of files) {
        const formData = new FormData();
        formData.append('file', file);

        try {
          const response = await API.post(FILE_URL, formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            }
          });

          if (response && response.status === 200) {
            image.push(response.data.files[0].originalname);
          }
        } catch (error) {
          console.error("File upload error: ", error);
        }
      }
    }

    if (subActivityName !== '') {
      const t: TaskItem[] = [
        { type: 'activity', name: subActivityName, images: image },
        { type: 'hazards', selected: [] },
        { type: 'consequence', option: [{ value: "", files: [], current_type: '', }] },
        { type: 'current_control', option: [{ value: "", files: [], current_type: '', method: '' }] },
        { type: 'assessment', severity: '', likelyhood: '', level: '' },
        { type: 'additional', accept: true },
        { type: 'responsibility', option: [{ current_type: '', person: '', date: null, value: '' }] },
        { type: 'reassessment', severity: '', likelyhood: '', level: '' },
        { type: 'activeStep', step: 0 },
        { type: 'stage', level: ['Hazards Identification', 'Consequences', 'Current Controls', 'Risk Estimation'] },
        { type: 'completed_stage', level: [] },
        {
          type: 'status', value: {
            hazardsIdentification: '',
            consequences: '',
            currentControls: '',
            riskEstimation: '',
            additionalControls: '',
          }
        }
      ];
      setTask((prev) => [...prev, t]);
      setSubActivityName('');
      setFiles([]);
      setAddSubActivity(false);
    }
  };

  const deleteTask = (e: React.MouseEvent, i: number) => {
    e.stopPropagation();
    const newTasks = task.filter((_, idx) => idx !== i);
    setTask(newTasks);
  };

  const openDialog = (item: TaskItem[], i: number) => {
    setItem('');
    setItem(item);
    setIndex(i);
    setVisible(true);
  };

  const editSubActivityTitle = (name: string, images: string[]) => {
    const updatedTask = task.map((item, i) => {
      if (i === index) {
        return item.map((ite) => {
          if (ite.type === 'activity') {
            return { ...ite, name: name, images: images };
          }
          return ite;
        });
      }
      return item;
    });
    setTask(updatedTask);
    setItem(updatedTask[index as number]);
  };

  // Event handlers for hazards, consequences, etc.
  const onClickHazards = (ha: any, j: number) => {
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'hazards') {
            if (ite.selected?.some((hazards: any) => hazards.id === ha.id)) {
              const hazardIndex = ite.selected?.findIndex((hazard: any) => hazard.id === ha.id);
              if (hazardIndex !== -1 && ite.selected) {
                const newHazards = [...ite.selected];
                newHazards.splice(hazardIndex, 1);
                ite.selected = newHazards;
              }
            } else {
              ite.selected?.push(ha);
            }
          }
        });
      }
      return item;
    });
    setTask(text);
    setItem(text[index as number]);
  };

  const onDeleteHaz = (item1: any) => {
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'hazards') {
            const hazardIndex = ite.selected?.findIndex((hazard: any) => hazard.id === item1.id);
            if (hazardIndex !== -1 && ite.selected) {
              const newHazards = [...ite.selected];
              newHazards.splice(hazardIndex, 1);
              ite.selected = newHazards;
            }
          }
        });
      }
      return item;
    });
    setTask(text);
    setItem(text[index as number]);
  };

  const onChangeSeverity = (e: any, type: string) => {
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === type) {
            ite.severity = e.value;
          }
        });
      }
      return item;
    });
    setTask(text);
    setItem(text[index as number]);
  };

  const onChangeLikelyhood = (e: any, type: string) => {
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === type) {
            ite.likelyhood = e.value;
          }
        });
      }
      return item;
    });
    setTask(text);
    setItem(text[index as number]);
  };

  const onChangeReAss = (value: boolean) => {
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'additional') {
            ite.accept = value;
          }
          if (ite.type === 'stage') {
            if (value === false) {
              if (!ite.level?.includes('Additional Controls')) {
                ite.level?.push('Additional Controls');
              }
            } else {
              ite.level = ite.level?.filter((item: string) => item !== 'Additional Controls');
            }
          }
        });
      }
      return item;
    });
    setTask(text);
    setItem(text[index as number]);
  };

  // Stepper functionality
  const sectionNames = [
    "Hazard Identification",
    "Consequences",
    "Current Controls",
    "Risk Estimation",
    "Additional Controls"
  ];

  const handleStageClick = (stageIndex: number) => {
    if (item && Array.isArray(item)) {
      const updatedItem = item.map((ite) => {
        if (ite.type === 'activeStep') {
          return { ...ite, step: stageIndex };
        }
        return ite;
      });
      setItem(updatedItem);
    }
  };

  const saveProgress = () => {
    // Save current progress without finalizing
    console.log('Progress saved');
  };

  const handleNext = () => {
    if (item && Array.isArray(item)) {
      const currentStep = item.find(i => i.type === 'activeStep')?.step || 0;
      const completedStages = item.find(i => i.type === 'completed_stage');

      if (completedStages && completedStages.level) {
        completedStages.level.push(currentStep);
        completedStages.level = [...new Set(completedStages.level)];
      }

      const updatedTask = task.map((taskItem, i) => {
        if (i === index) {
          return taskItem.map((ite) => {
            if (ite.type === 'status') {
              switch (currentStep) {
                case 0:
                  if (ite.value) ite.value.hazardsIdentification = 'completed';
                  break;
                case 1:
                  if (ite.value) ite.value.consequences = 'completed';
                  break;
                case 2:
                  if (ite.value) ite.value.currentControls = 'completed';
                  break;
                case 3:
                  if (ite.value) ite.value.riskEstimation = 'completed';
                  break;
                case 4:
                  if (ite.value) ite.value.additionalControls = 'completed';
                  break;
                default:
                  break;
              }
            }
            return ite;
          });
        }
        return taskItem;
      });

      setTask(updatedTask);
      setVisible(false);
    }
  };

  // Initialize data on component mount
  useEffect(() => {
    if (open) {
      getRoutineList();
      getHazardList();
      getAllResponsibility();
      getHighRiskHazardList();
    }
  }, [open]);

  return (
    <CustomModal
      isOpen={open}
      onClose={() => onOpenChange(false)}
      title="Add New Risk Assessment"
      size="xl"
    >
      <div className="risk-assessment-container">
        {/* Introduction Section */}
        <CustomCard className="intro-section">
          {type === 'routine' ? (
            <>
              <p className="intro-text">
                A risk evaluation conducted for activities that are regularly performed and require controls in place within the organization's operations.
              </p>
              <p className="guide-text">
                To proceed, select the relevant Department and Work Activity from the drop-down list of pre-configured options. If you cannot find a specific department or work activity, check the Risk Register, as a risk assessment may have already been completed for that activity.
              </p>
              <p className="guide-text">
                If the work activity is not routinely conducted by the organization and is therefore not found in the work activity register, switch to Non-Routine Risk Assessment and manually enter the title of the work to continue.
              </p>
            </>
          ) : (
            <p className="intro-text">
              An evaluation focused on activities that are not regularly performed and do not have established controls, requiring a detailed assessment to ensure appropriate safeguards are identified and implemented.
            </p>
          )}
        </CustomCard>

        {/* Department and Activity Selection */}
        <CustomCard title="Work Activity Selection" className="selection-section">
          {type === 'routine' ? (
            <div className="selection-grid">
              <div className="selection-item">
                <label className="field-label">Choose Operational Risk Area</label>
                <CustomSelect
                  options={depart.map((d: any) => ({ label: d.name, value: d.id }))}
                  value={selectedDepart?.id || ''}
                  onChange={(value) => {
                    const selected = depart.find((d: any) => d.id === value);
                    setSelectedDepart(selected);
                    setSelectedActivity(null);
                  }}
                  placeholder="Select Operational Risk Area"
                />
              </div>
              <div className="selection-item">
                <label className="field-label">Choose Work Activity</label>
                <CustomSelect
                  options={activity.map((a: any) => ({ label: a.name, value: a.id }))}
                  value={selectedActivity?.id || ''}
                  onChange={(value) => {
                    const selected = activity.find((a: any) => a.id === value);
                    setSelectedActivity(selected);
                  }}
                  placeholder="Select Work Activity"
                />
              </div>
            </div>
          ) : type === 'nonroutine' ? (
            <div className="selection-item">
              <label className="field-label">Work Activity Description</label>
              <CustomTextarea
                value={nonRoutineActivity}
                onChange={setNonRoutineActivity}
                placeholder="Enter the title of the work to continue..."
                rows={3}
              />
            </div>
          ) : (
            <div className="selection-item">
              <label className="field-label">Hazard Name</label>
              <CustomInput
                value={hazardName}
                onChange={setHazardName}
                placeholder="Enter hazard name"
              />
            </div>
          )}

          {/* Team Members Selection */}
          <div className="selection-item full-width">
            <label className="field-label">Risk Assessment Team Members</label>
            <p className="field-description">
              Identify the qualified RA Team Members to include in this Risk Assessment. Only qualified members will be listed. If a required member is not listed, please contact the Administrator to have them added.
            </p>
            <CustomMultiSelect
              options={crew}
              value={selectedCrew}
              onChange={setSelectedCrew}
              placeholder="Choose team members..."
            />
          </div>
        </CustomCard>

        {/* Additional Information Section */}
        <CustomCard title="Additional Information" className="info-section">
          <div className="info-item">
            <label className="field-label">Additional Work Activity Details (Optional)</label>
            <p className="field-description">
              Provide additional information about the work activity to clarify scope and context.
            </p>
            <CustomTextarea
              value={activityDesc}
              onChange={setActivityDesc}
              placeholder="Enter additional details about the work activity..."
              rows={3}
            />
          </div>
        </CustomCard>

        {/* Sub Activities Section */}
        <CustomCard title="Sub Activities" className="activities-section">
          <div className="activities-intro">
            <p className="section-description">
              Identify and list all sub-activities associated with the selected process. For each sub-activity, provide a clear description and, where feasible, upload any relevant images. These images will be utilized in the risk communication modules to enhance understanding and awareness of the process and associated risks.
            </p>
            <p className="section-guide">
              Click on each listed sub-activity and follow the provided guidance to identify the hazards, potential consequences, current controls, and assess the risks specific to that sub-activity.
            </p>
          </div>

          <div className="activities-list">
            {task.length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">📋</div>
                <p>No sub-activities added yet</p>
                <small>Click "Add Sub-Activity" to get started</small>
              </div>
            ) : (
              <div className="activities-grid">
                {task.map((item, i) => {
                  const activityItem = item.find(ite => ite.type === 'activity');
                  const statusItem = item.find(ite => ite.type === 'status');

                  const getStatusInfo = () => {
                    if (statusItem && statusItem.value) {
                      const statuses = Object.values(statusItem.value);
                      if (statuses.some((status: any) => status === 'completed')) {
                        return { label: 'Completed', class: 'status-completed' };
                      }
                      if (statuses.some((status: any) => status === 'inprogress')) {
                        return { label: 'In Progress', class: 'status-progress' };
                      }
                    }
                    return { label: 'Not Started', class: 'status-pending' };
                  };

                  const status = getStatusInfo();

                  return (
                    <CustomCard key={i} className="activity-card">
                      <div className="activity-header">
                        <div className="activity-info">
                          <h4 className="activity-title">{activityItem?.name || 'Unnamed Activity'}</h4>
                          <span className={`status-badge ${status.class}`}>
                            {status.label}
                          </span>
                        </div>
                        <div className="activity-actions">
                          <CustomButton
                            variant="outline"
                            size="sm"
                            onClick={() => openDialog(item, i)}
                          >
                            ✏️ Edit
                          </CustomButton>
                          <CustomButton
                            variant="danger"
                            size="sm"
                            onClick={() => deleteTask({} as React.MouseEvent, i)}
                          >
                            🗑️ Delete
                          </CustomButton>
                        </div>
                      </div>
                      {activityItem?.images && activityItem.images.length > 0 && (
                        <div className="activity-attachments">
                          <small>📎 {activityItem.images.length} image(s) attached</small>
                        </div>
                      )}
                    </CustomCard>
                  );
                })}
              </div>
            )}
          </div>

          <div className="add-activity-section">
            <CustomButton
              variant="outline"
              onClick={() => setAddSubActivity(!addSubActivity)}
            >
              ➕ Add Sub-Activity
            </CustomButton>
          </div>

          {addSubActivity && (
            <CustomCard className="add-activity-form">
              <div className="form-section">
                <label className="field-label">Sub-Activity Name</label>
                <CustomInput
                  value={subActivityName}
                  onChange={setSubActivityName}
                  placeholder="Enter sub-activity name..."
                />
              </div>

              <div className="form-section">
                <label className="field-label">Image Uploads</label>
                <CustomFileUpload
                  onFileSelect={handleFileChange}
                  accept="image/*"
                  multiple={true}
                  maxFiles={5}
                />
              </div>

              <div className="form-actions">
                <CustomButton onClick={AddSubActivityTitle}>
                  💾 Save Sub-Activity
                </CustomButton>
                <CustomButton
                  variant="secondary"
                  onClick={() => setAddSubActivity(false)}
                >
                  Cancel
                </CustomButton>
              </div>
            </CustomCard>
          )}
        </CustomCard>

        {/* Overall Recommendations Section */}
        <CustomCard title="Overall Recommendations of the RA Team" className="recommendations-section">
          <div className="recommendations-list">
            <div className="recommendation-item">
              <div className="recommendation-number">1</div>
              <div className="recommendation-content">
                <label className="field-label">Risk Level Assessment</label>
                <CustomSelect
                  options={[
                    { label: 'The overall risk level for this work activity is LOW. No further additional controls actions are necessary. Monitoring is required to ensure that the controls are maintained.', value: '0' },
                    { label: 'The overall risk level for this work activity is MEDIUM. Work can progress with close supervision and monitoring of current controls. Additional controls identified should be implemented within the defined period of time.', value: '1' },
                    { label: 'The overall risk level for this work activity is HIGH. Work should not be started or continued until the risk level has been reduced and risk numbers enters the LOW or MEDIUM zone.', value: '2' }
                  ]}
                  value={recommendationOne?.value || ''}
                  onChange={(value) => setRecommendationOne({ value, label: '' })}
                  placeholder="Choose risk level assessment..."
                />
              </div>
            </div>

            <div className="recommendation-item">
              <div className="recommendation-number">2</div>
              <div className="recommendation-content">
                <label className="field-label">Permit Requirements</label>
                <CustomSelect
                  options={[
                    { label: 'Since this is routine activity with low risk, no formal permit is recommended. However, a dynamic review is advised in case of changing circumstances.', value: '0' },
                    { label: 'A formal permit to work is required before work can commence. Duration will be specified by the permit approver.', value: '1' },
                    { label: 'A formal permit to work that is required. This is valid for a specific task and limited duration. The permit shall include the names of individuals involved in the work.', value: '2' }
                  ]}
                  value={recommendationTwo?.value || ''}
                  onChange={(value) => setRecommendationTwo({ value, label: '' })}
                  placeholder="Choose permit requirements..."
                />
              </div>
            </div>
          </div>
        </CustomCard>

        {/* High-Risk Permits Section */}
        <CustomCard title="High-Risk Permits" className="permits-section">
          <div className="recommendation-item">
            <div className="recommendation-number">3</div>
            <div className="recommendation-content">
              <label className="field-label">Required High-Risk Permits</label>
              <p className="field-description">
                Considering the hazards and risks associated with this work activity, the RA team requires the following high-risk permits to be approved and active when applying for a permit for this specific activity:
              </p>
              <div className="permits-grid">
                {risk.length === 0 ? (
                  <p className="no-permits">No high-risk permits available</p>
                ) : (
                  risk.map((item: any, index: number) => (
                    <CustomCheckbox
                      key={index}
                      checked={eptwHot.some((item1: any) => item1.id === item.id)}
                      onChange={(checked) => {
                        if (checked) {
                          setEptwHot((prev) => [...prev, item]);
                        } else {
                          setEptwHot(prevData => prevData.filter((item1: any) => item1.id !== item.id));
                        }
                      }}
                      label={item.hazardName}
                      className="permit-checkbox"
                    />
                  ))
                )}
              </div>
            </div>
          </div>

          <div className="recommendation-item">
            <div className="recommendation-number">4</div>
            <div className="recommendation-content">
              <label className="field-label">Additional Recommendations</label>
              <CustomTextarea
                value={additionalRecommendation}
                onChange={setAdditionalRecommendation}
                placeholder="Enter any additional recommendations..."
                rows={3}
              />
            </div>
          </div>
        </CustomCard>

        {/* Declaration Section */}
        <CustomCard title="Declaration" className="declaration-section">
          <div className="declaration-content">
            <div className="declaration-text">
              {type === 'routine' ? (
                <p>
                  As the Team Leader for this Routine Risk Assessment, I affirm my role in guiding the identification of potential risks and necessary controls. The controls listed have been evaluated by the team based on their professional expertise and are essential for ensuring safety in this activity. This outcome reflects the collective judgment of the team, reached through collaboration and consensus.
                </p>
              ) : type === 'nonroutine' ? (
                <p>
                  As the Team Leader for this Non-Routine Risk Assessment, I confirm my responsibility in guiding the team through the identification of potential risks and necessary controls for activities that are not part of the organization's routine work activity inventory. Given that these activities are not regularly undertaken, we are placing additional focus on documenting the risks and ensuring that appropriate controls are in place. The controls identified are essential for maintaining safety, and this conclusion has been reached through consensus based on the team's collective professional judgment and experience.
                </p>
              ) : (
                <p>
                  As the Team Leader for this High-Risk Hazard Assessment, I confirm my responsibility in guiding the team through the identification of potential risks and necessary controls for high-risk activities. The controls identified are essential for maintaining safety, and this conclusion has been reached through consensus based on the team's collective professional judgment and experience.
                </p>
              )}
            </div>

            <div className="signature-section">
              <label className="field-label">Team Leader Signature</label>
              <div className="signature-container">
                <SignatureCanvas
                  penColor="#1F3BB3"
                  canvasProps={{
                    width: 450,
                    height: 120,
                    className: "signature-canvas",
                  }}
                  ref={signRef}
                />
                <CustomButton
                  variant="secondary"
                  size="sm"
                  onClick={() => signRef.current?.clear()}
                  className="clear-signature"
                >
                  🗑️ Clear
                </CustomButton>
              </div>
              <p className="signee-name">{user?.firstName || 'Team Leader'}</p>
            </div>
          </div>
        </CustomCard>

        {/* Action Buttons */}
        <div className="action-buttons">
          <CustomButton
            variant="outline"
            onClick={() => {
              onSubmit({
                type,
                task,
                selectedDepart,
                selectedActivity,
                selectedCrew,
                activityDesc,
                recommendationOne,
                recommendationTwo,
                additionalRecommendation,
                eptwHot,
                nonRoutineDepartment,
                nonRoutineActivity,
                hazardName,
                shortName
              }, true);
            }}
          >
            💾 Save as Draft
          </CustomButton>
          <CustomButton
            onClick={() => {
              onSubmit({
                type,
                task,
                selectedDepart,
                selectedActivity,
                selectedCrew,
                activityDesc,
                recommendationOne,
                recommendationTwo,
                additionalRecommendation,
                eptwHot,
                nonRoutineDepartment,
                nonRoutineActivity,
                hazardName,
                shortName
              }, false);
            }}
          >
            🚀 Release Draft for Affirmation
          </CustomButton>
        </div>
      </div>
    </CustomModal>

    {/* Sub-Activity Modal */}
    {item !== '' && Array.isArray(item) && (
      <CustomModal
        isOpen={visible}
        onClose={() => setVisible(false)}
        title="Sub-activity Risk Assessment"
        size="xl"
      >
        <div className="sub-activity-modal">
          {/* Activity Header */}
          <CustomCard className="activity-header-card">
            <div className="activity-edit-section">
              <label className="field-label">Sub-Activity Name</label>
              <CustomInput
                value={item.find(i => i.type === 'activity')?.name || ''}
                onChange={(value) => {
                  const images = item.find(i => i.type === 'activity')?.images || [];
                  editSubActivityTitle(value, images);
                }}
                placeholder="Enter sub-activity name..."
              />
            </div>
          </CustomCard>

          {/* Stepper */}
          {(() => {
            const activeStepItem = item.find(i => i.type === 'activeStep');
            const stageItem = item.find(i => i.type === 'stage');
            const completedStageItem = item.find(i => i.type === 'completed_stage');

            return activeStepItem && stageItem && (
              <CustomStepper
                steps={stageItem.level || []}
                activeStep={activeStepItem.step || 0}
                onStepClick={handleStageClick}
                completedSteps={completedStageItem?.level || []}
              />
            );
          })()}

          {/* Step Content */}
          <CustomCard className="step-content-card">
            {(() => {
              const activeStepItem = item.find(i => i.type === 'activeStep');
              const currentStep = activeStepItem?.step || 0;

              switch (currentStep) {
                case 0:
                  return (
                    <div className="hazards-step">
                      <div className="step-header">
                        <h3>🚨 Hazards Identification</h3>
                        <p>For this sub-activity, identify associated hazards by selecting from the various hazard types displayed. Each type will feature icons representing specific hazards.</p>
                        <p><small>If you encounter a specific hazard that is not included in the library, please contact support to have it added.</small></p>
                      </div>

                      {/* Hazards Selection */}
                      <CustomTabs
                        tabs={hazards.map((hazard: any) => ({
                          label: hazard.name,
                          content: (
                            <div className="hazards-grid">
                              {hazard.hazardItems?.map((hazardItem: any, j: number) => {
                                const hazardData = item.find(i => i.type === 'hazards');
                                const isSelected = hazardData?.selected?.some((h: any) => h.id === hazardItem.id);

                                return (
                                  <div
                                    key={j}
                                    className={`hazard-item ${isSelected ? 'selected' : ''}`}
                                    onClick={() => onClickHazards(hazardItem, j)}
                                  >
                                    <div className="hazard-icon">⚠️</div>
                                    <div className="hazard-name">{hazardItem.name}</div>
                                  </div>
                                );
                              })}
                            </div>
                          )
                        }))}
                        activeTab={activeTabIndex}
                        onTabChange={setActiveTabIndex}
                      />

                      {/* Selected Hazards */}
                      {(() => {
                        const hazardData = item.find(i => i.type === 'hazards');
                        const selectedHazards = hazardData?.selected || [];

                        return selectedHazards.length > 0 && (
                          <div className="selected-hazards">
                            <h4>✅ Selected Hazards</h4>
                            <div className="hazards-list">
                              {selectedHazards.map((hazardItem: any, index: number) => (
                                <div key={index} className="selected-hazard-item">
                                  <span>{hazardItem.name}</span>
                                  <CustomButton
                                    variant="danger"
                                    size="sm"
                                    onClick={() => onDeleteHaz(hazardItem)}
                                  >
                                    ✕
                                  </CustomButton>
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      })()}
                    </div>
                  );

                case 1:
                  return (
                    <div className="consequences-step">
                      <div className="step-header">
                        <h3>📋 Consequences</h3>
                        <p>For this sub-activity, list and elaborate on all potential consequences associated with the identified hazards, covering impacts on Personnel, Environment, Property/Equipment, and Operations.</p>
                      </div>
                      <div className="placeholder-content">
                        <p>🚧 Consequences assessment panel will be implemented here</p>
                      </div>
                    </div>
                  );

                case 2:
                  return (
                    <div className="controls-step">
                      <div className="step-header">
                        <h3>🛡️ Current Controls</h3>
                        <p>Identify and describe in detail the current controls in place to manage the hazards and minimize their consequences.</p>
                      </div>
                      <div className="placeholder-content">
                        <p>🚧 Current controls panel will be implemented here</p>
                      </div>
                    </div>
                  );

                case 3:
                  return (
                    <div className="assessment-step">
                      <div className="step-header">
                        <h3>📊 Risk Assessment</h3>
                        <p>Assess the risk level by evaluating the likelihood and severity of potential consequences.</p>
                      </div>
                      <div className="placeholder-content">
                        <p>🚧 Risk assessment panel will be implemented here</p>
                      </div>
                    </div>
                  );

                case 4:
                  return (
                    <div className="additional-controls-step">
                      <div className="step-header">
                        <h3>➕ Additional Controls</h3>
                        <p>Identify additional risk management measures that should be implemented to further reduce risks.</p>
                      </div>
                      <div className="placeholder-content">
                        <p>🚧 Additional controls panel will be implemented here</p>
                      </div>
                    </div>
                  );

                default:
                  return (
                    <div className="default-step">
                      <p>Select a step to begin the risk assessment process.</p>
                    </div>
                  );
              }
            })()}
          </CustomCard>

          {/* Modal Actions */}
          <div className="modal-actions">
            <CustomButton
              variant="outline"
              onClick={saveProgress}
            >
              💾 Save Progress
            </CustomButton>
            <CustomButton
              onClick={handleNext}
            >
              ✅ Save & Finalize {(() => {
                const activeStepItem = item.find(i => i.type === 'activeStep');
                const currentStep = activeStepItem?.step || 0;
                return sectionNames[currentStep] || 'Section';
              })()}
            </CustomButton>
          </div>
        </div>
      </CustomModal>
    )}
  </>
  );
};

export default AddRiskAssessmentModal;

// Comprehensive CSS styles for custom components
const styles = `
/* Custom Modal Styles */
.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.custom-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.custom-modal-sm { width: 400px; }
.custom-modal-md { width: 600px; }
.custom-modal-lg { width: 800px; }
.custom-modal-xl { width: 95vw; max-width: 1400px; }

.modal-header {
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

/* Custom Input Styles */
.custom-input, .custom-textarea, .custom-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s;
  background: white;
}

.custom-input:focus, .custom-textarea:focus, .custom-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.custom-textarea {
  resize: vertical;
  min-height: 80px;
}

/* Custom Button Styles */
.custom-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.custom-btn-sm { padding: 8px 16px; font-size: 12px; }
.custom-btn-lg { padding: 16px 32px; font-size: 16px; }

.custom-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.custom-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.custom-btn-secondary {
  background: #6b7280;
  color: white;
}

.custom-btn-secondary:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

.custom-btn-danger {
  background: #ef4444;
  color: white;
}

.custom-btn-danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.custom-btn-outline {
  background: transparent;
  color: #3b82f6;
  border: 2px solid #3b82f6;
}

.custom-btn-outline:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-1px);
}

.custom-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Custom Card Styles */
.custom-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.card-header {
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  font-size: 18px;
  color: #1f2937;
}

.card-body {
  padding: 24px;
}

/* Risk Assessment Container */
.risk-assessment-container {
  max-width: 100%;
}

/* Introduction Section */
.intro-section .intro-text {
  font-style: italic;
  color: #4b5563;
  font-size: 16px;
  margin-bottom: 16px;
}

.intro-section .guide-text {
  color: #6b7280;
  margin-bottom: 12px;
  line-height: 1.6;
}

/* Selection Section */
.selection-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 24px;
  margin-bottom: 24px;
}

.selection-item {
  margin-bottom: 20px;
}

.selection-item.full-width {
  grid-column: 1 / -1;
}

.field-label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 14px;
}

.field-description {
  color: #6b7280;
  font-size: 13px;
  margin-bottom: 12px;
  line-height: 1.5;
}

/* Multi-select Styles */
.custom-multiselect {
  position: relative;
}

.multiselect-trigger {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  background: white;
  transition: all 0.2s;
}

.multiselect-trigger:hover {
  border-color: #3b82f6;
}

.multiselect-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 2px solid #e5e7eb;
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
}

.multiselect-option {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.multiselect-option:hover {
  background: #f3f4f6;
}

.multiselect-option.selected {
  background: #dbeafe;
  color: #1d4ed8;
}

/* Activities Section */
.activities-intro {
  margin-bottom: 24px;
}

.section-description {
  color: #4b5563;
  margin-bottom: 12px;
  line-height: 1.6;
}

.section-guide {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.activities-grid {
  display: grid;
  gap: 16px;
  margin-bottom: 24px;
}

.activity-card {
  border: 2px solid #e5e7eb;
  transition: all 0.2s;
}

.activity-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.activity-info {
  flex: 1;
}

.activity-title {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 18px;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-completed {
  background: #dcfce7;
  color: #166534;
}

.status-progress {
  background: #fef3c7;
  color: #92400e;
}

.status-pending {
  background: #f3f4f6;
  color: #6b7280;
}

.activity-actions {
  display: flex;
  gap: 8px;
}

.activity-attachments {
  color: #6b7280;
  font-size: 12px;
}

/* File Upload Styles */
.custom-file-upload {
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 32px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: #f9fafb;
}

.custom-file-upload:hover, .custom-file-upload.drag-over {
  border-color: #3b82f6;
  background: #dbeafe;
}

.upload-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.upload-text p {
  margin: 0 0 4px 0;
  color: #374151;
  font-weight: 500;
}

.upload-text small {
  color: #6b7280;
}

/* Recommendations Section */
.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.recommendation-item {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.recommendation-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.recommendation-content {
  flex: 1;
}

/* Permits Section */
.permits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.no-permits {
  color: #6b7280;
  font-style: italic;
  text-align: center;
  padding: 24px;
}

/* Custom Checkbox */
.custom-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.custom-checkbox:hover {
  background: #f3f4f6;
}

.custom-checkbox input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s;
}

.custom-checkbox input[type="checkbox"]:checked + .checkbox-custom {
  background: #3b82f6;
  border-color: #3b82f6;
}

.custom-checkbox input[type="checkbox"]:checked + .checkbox-custom::after {
  content: "✓";
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-label {
  color: #374151;
  font-size: 14px;
}

/* Declaration Section */
.declaration-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.declaration-text p {
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
}

.signature-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.signature-container {
  position: relative;
  display: inline-block;
}

.signature-canvas {
  border: 2px solid #d1d5db;
  border-radius: 8px;
  background: white;
}

.clear-signature {
  position: absolute;
  top: 8px;
  right: 8px;
}

.signee-name {
  text-align: center;
  color: #6b7280;
  font-weight: 500;
  margin: 8px 0 0 0;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

/* Custom Tabs */
.custom-tabs {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.tabs-header {
  display: flex;
  background: #f3f4f6;
  border-bottom: 1px solid #e5e7eb;
}

.tab-button {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.tab-button:hover {
  background: #e5e7eb;
}

.tab-button.active {
  background: white;
  color: #3b82f6;
  border-bottom: 2px solid #3b82f6;
}

.tabs-content {
  padding: 20px;
  background: white;
}

/* Custom Stepper */
.custom-stepper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background: #f8fafc;
  border-radius: 12px;
  margin: 24px 0;
}

.stepper-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.stepper-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 8px;
}

.stepper-circle.pending {
  background: #e5e7eb;
  color: #6b7280;
}

.stepper-circle.active {
  background: #3b82f6;
  color: white;
  transform: scale(1.1);
}

.stepper-circle.completed {
  background: #10b981;
  color: white;
}

.stepper-label {
  font-size: 12px;
  text-align: center;
  color: #6b7280;
  max-width: 80px;
}

.stepper-line {
  position: absolute;
  top: 20px;
  left: 50%;
  right: -50%;
  height: 2px;
  background: #e5e7eb;
  z-index: 1;
}

.stepper-line.completed {
  background: #10b981;
}

/* Sub-activity Modal */
.sub-activity-modal {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.activity-header-card {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.activity-edit-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step-content-card {
  min-height: 400px;
}

.step-header {
  margin-bottom: 24px;
}

.step-header h3 {
  margin: 0 0 12px 0;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-header p {
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

.placeholder-content {
  text-align: center;
  padding: 48px;
  color: #6b7280;
  background: #f9fafb;
  border-radius: 8px;
  border: 2px dashed #d1d5db;
}

/* Hazards Step */
.hazards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.hazard-item {
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.hazard-item:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
}

.hazard-item.selected {
  border-color: #3b82f6;
  background: #dbeafe;
  color: #1d4ed8;
}

.hazard-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.hazard-name {
  font-weight: 500;
  font-size: 14px;
}

.selected-hazards {
  margin-top: 32px;
  padding: 20px;
  background: #f0fdf4;
  border-radius: 8px;
  border: 1px solid #bbf7d0;
}

.selected-hazards h4 {
  margin: 0 0 16px 0;
  color: #166534;
}

.hazards-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-hazard-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: white;
  border: 1px solid #bbf7d0;
  border-radius: 20px;
  font-size: 14px;
}

.modal-actions {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

/* Form Sections */
.form-section {
  margin-bottom: 20px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.add-activity-form {
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
}

.add-activity-section {
  text-align: center;
  margin-top: 24px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .custom-modal-xl {
    width: 95vw;
    height: 95vh;
  }

  .selection-grid {
    grid-template-columns: 1fr;
  }

  .activities-grid {
    grid-template-columns: 1fr;
  }

  .permits-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .modal-actions {
    flex-direction: column;
  }

  .custom-stepper {
    flex-direction: column;
    gap: 16px;
  }

  .stepper-item {
    flex-direction: row;
    width: 100%;
  }

  .stepper-line {
    display: none;
  }
}

/* Animation Classes */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.custom-card {
  animation: fadeIn 0.3s ease-out;
}

.hazard-item {
  animation: fadeIn 0.3s ease-out;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 12px; }
.mb-4 { margin-bottom: 16px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 12px; }
.mt-4 { margin-top: 16px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}
