import React, { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Button } from 'primereact/button';
import { Dialog } from 'primereact/dialog';
import { DropzoneArea } from 'material-ui-dropzone';
import Select from 'react-select';
import SignatureCanvas from "react-signature-canvas";
import Swal from 'sweetalert2';
import { Modal } from 'react-bootstrap';
import { TabView, TabPanel } from 'primereact/tabview';
import { Stepper, Step, StepLabel, Typography } from '@mui/material';
import { Calendar } from 'primereact/calendar';
import Accordion from 'react-bootstrap/Accordion';
import { RadioButton } from 'primereact/radiobutton';
import { Checkbox } from 'primereact/checkbox';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import moment from 'moment';

// Import the components you provided
import SubActivityComponent from './Component/SubActivityComponent';
import HeadStepper from './Component/HeadStepper';

// Import API and constants
import API from '../../services/API';
import {
  FILE_URL,
  HAZARDS_CATEGOTY,
  GET_ALL_USER,
  RISKASSESSMENT_LIST,
  GET_RISK_HAZARD_URL
} from '../../constants';

// Types based on test.js structure
interface TaskItem {
  type: string;
  name?: string;
  images?: string[];
  selected?: any[];
  option?: any[];
  severity?: string;
  likelyhood?: string;
  level?: string;
  accept?: boolean;
  step?: number;
  value?: any;
  person?: string;
  date?: Date | null;
  current_type?: string;
  method?: string;
  files?: string[];
  required?: boolean;
  validity?: boolean;
}

// Static data based on test.js structure
const severity = [
  { "value": "1", "label": "1(E) - Negligible" },
  { "value": "2", "label": "2(D) - Minor" },
  { "value": "3", "label": "3(C) - Moderate" },
  { "value": "4", "label": "4(B) - Major" },
  { "value": "5", "label": "5(A) - Catastrophic" }
];

const likelyhood = [
  { label: "Rare (1)", value: "1" },
  { label: "Unlikely (2)", value: "2" },
  { label: "Possible (3)", value: "3" },
  { label: "Likely (4)", value: "4" },
  { label: "Almost Certain (5)", value: "5" },
];

const impactOn = [
  { 'label': 'Personnel', 'value': 'Personnel' },
  { 'label': 'Environment', 'value': 'Environment' },
  { 'label': 'Property / Equipment', 'value': 'Property / Equipment' },
  { 'label': 'Operations', 'value': 'Operations' },
];

const control = [
  { 'label': 'No Control', 'value': 'No Control' },
  { 'label': 'Engineering', 'value': 'Engineering' },
  { 'label': 'Administrative', 'value': 'Administrative' },
  { 'label': 'PPE', 'value': 'PPE' }
];

const controlAdditional = [
  { 'label': 'Elimination', 'value': 'Elimination' },
  { 'label': 'Substitution', 'value': 'Substitution' },
  { 'label': 'Engineering', 'value': 'Engineering' },
  { 'label': 'Administrative', 'value': 'Administrative' },
  { 'label': 'PPE', 'value': 'PPE' }
];

const controlType = [
  { 'label': 'Preventative', 'value': 'Preventative' },
  { 'label': 'Mitigative', 'value': 'Mitigative' }
];

const severityData = [
  {
    id: '5 (A)',
    severity: 'Catastrophic',
    personnel: 'Serious injury with long-term or permanent disability or death.',
    property: 'Significant damage leading to major repairs.',
    environment: 'Significant environmental damage requiring regulatory reporting and cleanup.',
    serviceLoss: 'Major disruption to service operations, extended recovery time.'
  },
  {
    id: '4 (B)',
    severity: 'Major',
    personnel: 'Serious injury with long-term recovery or permanent disability.',
    property: 'Noticeable damage requiring repair, causing moderate downtime or repair costs.',
    environment: 'Moderate, recoverable environmental impact, requiring external agency notification or cleanup.',
    serviceLoss: 'Significant downtime with substantial recovery efforts.'
  },
  {
    id: '3 (C)',
    severity: 'Moderate',
    personnel: 'Injury requiring medical treatment with potential for short-term lost workdays or restricted duties.',
    property: 'Noticeable damage requiring repair, causing moderate downtime or repair costs.',
    environment: 'Moderate environmental impact, manageable on-site with potential regulatory notification.',
    serviceLoss: 'Moderate service interruption with short recovery.'
  },
  {
    id: '2 (D)',
    severity: 'Minor',
    personnel: 'Minor injury requiring first-aid or outpatient treatment, with minimal lost time.',
    property: 'Slight damage requiring minor repairs without significant downtime.',
    environment: 'Small localized impact, manageable on-site, without long-term environmental damage.',
    serviceLoss: 'Brief disruption to services, easily restored.'
  },
  {
    id: '1 (E)',
    severity: 'Insignificant',
    personnel: 'Minor first-aid required with no lost time or long-term health impacts.',
    property: 'Minimal damage or wear that does not require repair or interruption to operations.',
    environment: 'Negligible environmental impact with no regulatory involvement needed.',
    serviceLoss: 'No impact on services.'
  }
];

const levelData = [
  {
    level: '1',
    descriptor: 'Rare',
    detailedDescription: 'The event is highly unlikely to occur under normal circumstances, with little to no historical precedent.'
  },
  {
    level: '2',
    descriptor: 'Unlikely',
    detailedDescription: 'The event is improbable but could potentially happen under unusual conditions, though there is limited historical data to support this.'
  },
  {
    level: '3',
    descriptor: 'Possible',
    detailedDescription: 'The event could happen, with moderate chances of occurring based on historical records or foreseeable conditions.'
  },
  {
    level: '4',
    descriptor: 'Likely',
    detailedDescription: 'The event is expected to occur in the normal course of operations, with a significant history of similar incidents.'
  },
  {
    level: '5',
    descriptor: 'Almost Certain',
    detailedDescription: 'The event is highly likely to occur and is anticipated in the near future without further intervention.'
  }
];

const tableData = [
  { id: '5(A)', severity: 'Catastrophic', rare: '5(A)', unlikely: '10(A)', possible: '15(A)', likely: '20(A)', almostCertain: '25(A)' },
  { id: '4(B)', severity: 'Major', rare: '4(B)', unlikely: '8(B)', possible: '12(B)', likely: '16(B)', almostCertain: '20(B)' },
  { id: '3(C)', severity: 'Moderate', rare: '3(C)', unlikely: '6(C)', possible: '9(C)', likely: '12(C)', almostCertain: '15(C)' },
  { id: '2(D)', severity: 'Minor', rare: '2(D)', unlikely: '4(D)', possible: '6(D)', likely: '8(D)', almostCertain: '10(D)' },
  { id: '1(E)', severity: 'Insignificant', rare: '1(E)', unlikely: '2(E)', possible: '3(E)', likely: '4(E)', almostCertain: '5(E)' },
];

// Utility functions
const getStatusClass = (task: TaskItem[]) => {
  if (!task || !task[11] || !task[11].value) return 'bg-secondary';

  const statuses = Object.values(task[11].value);
  if (statuses.some((status: any) => status === 'completed')) return 'bg-success';
  if (statuses.some((status: any) => status === 'inprogress')) return 'bg-warning';
  return 'bg-secondary';
};

const rowClassName = (data: any) => {
  switch (data.level[0]) {
    case '1': return 'level-1';
    case '2': return 'level-2';
    case '3': return 'level-3';
    case '4': return 'level-4';
    case '5': return 'level-5';
    default: return '';
  }
};

const cellClassName = (value: any) => {
  const numericValue = parseInt(String(value).replace(/[^\d]/g, ''), 10);
  if (numericValue === 0) return '';
  if (numericValue === 1 || numericValue === 2 || numericValue === 3 || numericValue === 4) return 'cell-green';
  if (numericValue === 15 || numericValue === 20 || numericValue === 25 || numericValue === 16) return 'cell-red';
  return 'cell-yellow';
};

const cellStyle = (data: any, field: string) => cellClassName(data[field]);

interface AddRiskAssessmentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: any, isDraft: boolean) => void;
  type: 'routine' | 'nonroutine' | 'highrisk';
}

const AddRiskAssessmentModal: React.FC<AddRiskAssessmentModalProps> = ({
  open,
  onOpenChange,
  onSubmit,
  type = 'routine'
}) => {
  const user = useSelector((state: any) => state.login.user);
  const signRef = useRef<SignatureCanvas>(null);

  // State variables based on test.js structure
  const [files, setFiles] = useState<File[]>([]);
  const [depart, setDepart] = useState<any[]>([]);
  const [activity, setActivity] = useState<any[]>([]);
  const [crew, setCrew] = useState<any[]>([]);
  const [selectedDepart, setSelectedDepart] = useState<any>(null);
  const [selectedActivity, setSelectedActivity] = useState<any>(null);
  const [selectedCrew, setSelectedCrew] = useState<any[]>([]);
  const [addSubActivity, setAddSubActivity] = useState(false);
  const [activityDesc, setActivityDesc] = useState('');
  const [task, setTask] = useState<TaskItem[][]>([]);
  const [subActivityName, setSubActivityName] = useState('');
  const [visible, setVisible] = useState(false);
  const [item, setItem] = useState<TaskItem[] | ''>('');
  const [index, setIndex] = useState<number | ''>('');
  const [hazards, setHazards] = useState<any[]>([]);
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [responsibility, setResponsibility] = useState<any[]>([]);
  const [required, setRequired] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [risk, setRisk] = useState<any[]>([]);
  const [eptwHot, setEptwHot] = useState<any[]>([]);
  const [recommendationOne, setRecommendationOne] = useState<any>(null);
  const [recommendationTwo, setRecommendationTwo] = useState<any>(null);
  const [additionalRecommendation, setAdditionalRecommendation] = useState('');
  const [nonRoutineActivity, setNonRoutineActivity] = useState('');
  const [nonRoutineDepartment, setNonRoutineDepartment] = useState('');
  const [hazardName, setHazardName] = useState('');
  const [shortName, setShortName] = useState('');

  // API functions based on test.js
  const getRoutineList = async () => {
    try {
      const response = await API.get(RISKASSESSMENT_LIST);
      if (response.status === 200) {
        setDepart(response.data);
      }
    } catch (error) {
      console.error('Error fetching routine list:', error);
    }
  };

  const getHazardList = async () => {
    try {
      const selectedIndustry = localStorage.getItem('SELECTED_INDUSTRIES');

      if (selectedIndustry) {
        const selectedIndustryNames = selectedIndustry
          .split(',')
          .map(name => name.trim());

        const response = await API.get(GET_RISK_HAZARD_URL);
        if (response.status === 200) {
          const industryList = response.data;
          const matchedIndustries = industryList.filter((item: any) =>
            selectedIndustryNames.includes(item.name)
          );
          const allHazards = matchedIndustries.flatMap(
            (industry: any) => industry.hazardCategories || []
          );
          setHazards(allHazards);
        }
      } else {
        const response = await API.get(HAZARDS_CATEGOTY);
        if (response.status === 200) {
          const data = response.data.filter(
            (item: any) => item.name !== 'Hazard-Based'
          );
          setHazards(data);
        }
      }
    } catch (error) {
      console.error('Error fetching hazard list:', error);
    }
  };

  const getAllResponsibility = async () => {
    try {
      const response = await API.get(GET_ALL_USER);
      if (response.status === 200) {
        const depart = response.data.map((item: any) => ({
          id: item.id,
          name: item.firstName,
          email: item.email
        }));
        setResponsibility(depart);
      }
    } catch (error) {
      console.error('Error fetching responsibility list:', error);
    }
  };

  const getHighRiskHazardList = async () => {
    try {
      const response = await API.get(RISKASSESSMENT_LIST);
      if (response.status === 200) {
        setRisk(response.data);
      }
    } catch (error) {
      console.error('Error fetching high-risk hazard list:', error);
    }
  };

  // File handling
  const handleFileChange = (files: File[]) => {
    setFiles(files);
  };

  // Sub-activity management based on test.js structure
  const AddSubActivityTitle = async () => {
    const image: string[] = [];

    if (files.length > 0) {
      for (const file of files) {
        const formData = new FormData();
        formData.append('file', file);

        try {
          const response = await API.post(FILE_URL, formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            }
          });

          if (response && response.status === 200) {
            image.push(response.data.files[0].originalname);
          }
        } catch (error) {
          console.error("File upload error: ", error);
        }
      }
    }

    if (subActivityName !== '') {
      const t: TaskItem[] = [
        { type: 'activity', name: subActivityName, images: image },
        { type: 'hazards', selected: [] },
        { type: 'consequence', option: [{ value: "", files: [], current_type: '', }] },
        { type: 'current_control', option: [{ value: "", files: [], current_type: '', method: '' }] },
        { type: 'assessment', severity: '', likelyhood: '', level: '' },
        { type: 'additional', accept: true },
        { type: 'responsibility', option: [{ current_type: '', person: '', date: null, value: '' }] },
        { type: 'reassessment', severity: '', likelyhood: '', level: '' },
        { type: 'activeStep', step: 0 },
        { type: 'stage', level: ['Hazards Identification', 'Consequences', 'Current Controls', 'Risk Estimation'] },
        { type: 'completed_stage', level: [] },
        {
          type: 'status', value: {
            hazardsIdentification: '',
            consequences: '',
            currentControls: '',
            riskEstimation: '',
            additionalControls: '',
          }
        }
      ];
      setTask((prev) => [...prev, t]);
      setSubActivityName('');
      setFiles([]);
      setAddSubActivity(false);
    }
  };

  const deleteTask = (e: React.MouseEvent, i: number) => {
    e.stopPropagation();
    const newTasks = task.filter((_, idx) => idx !== i);
    setTask(newTasks);
  };

  const openDialog = (item: TaskItem[], i: number) => {
    setItem('');
    setItem(item);
    setIndex(i);
    setVisible(true);
  };

  const editSubActivityTitle = (name: string, images: string[]) => {
    const updatedTask = task.map((item, i) => {
      if (i === index) {
        return item.map((ite) => {
          if (ite.type === 'activity') {
            return { ...ite, name: name, images: images };
          }
          return ite;
        });
      }
      return item;
    });
    setTask(updatedTask);
    setItem(updatedTask[index as number]);
  };

  // Event handlers for hazards, consequences, etc.
  const onClickHazards = (ha: any, j: number) => {
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'hazards') {
            if (ite.selected?.some((hazards: any) => hazards.id === ha.id)) {
              const hazardIndex = ite.selected?.findIndex((hazard: any) => hazard.id === ha.id);
              if (hazardIndex !== -1 && ite.selected) {
                const newHazards = [...ite.selected];
                newHazards.splice(hazardIndex, 1);
                ite.selected = newHazards;
              }
            } else {
              ite.selected?.push(ha);
            }
          }
        });
      }
      return item;
    });
    setTask(text);
    setItem(text[index as number]);
  };

  const onDeleteHaz = (item1: any) => {
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'hazards') {
            const hazardIndex = ite.selected?.findIndex((hazard: any) => hazard.id === item1.id);
            if (hazardIndex !== -1 && ite.selected) {
              const newHazards = [...ite.selected];
              newHazards.splice(hazardIndex, 1);
              ite.selected = newHazards;
            }
          }
        });
      }
      return item;
    });
    setTask(text);
    setItem(text[index as number]);
  };

  const onChangeSeverity = (e: any, type: string) => {
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === type) {
            ite.severity = e.value;
          }
        });
      }
      return item;
    });
    setTask(text);
    setItem(text[index as number]);
  };

  const onChangeLikelyhood = (e: any, type: string) => {
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === type) {
            ite.likelyhood = e.value;
          }
        });
      }
      return item;
    });
    setTask(text);
    setItem(text[index as number]);
  };

  const onChangeReAss = (value: boolean) => {
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'additional') {
            ite.accept = value;
          }
          if (ite.type === 'stage') {
            if (value === false) {
              if (!ite.level?.includes('Additional Controls')) {
                ite.level?.push('Additional Controls');
              }
            } else {
              ite.level = ite.level?.filter((item: string) => item !== 'Additional Controls');
            }
          }
        });
      }
      return item;
    });
    setTask(text);
    setItem(text[index as number]);
  };

  // Stepper functionality
  const sectionNames = [
    "Hazard Identification",
    "Consequences",
    "Current Controls",
    "Risk Estimation",
    "Additional Controls"
  ];

  const handleStageClick = (stageIndex: number) => {
    if (item && Array.isArray(item)) {
      const updatedItem = item.map((ite) => {
        if (ite.type === 'activeStep') {
          return { ...ite, step: stageIndex };
        }
        return ite;
      });
      setItem(updatedItem);
    }
  };

  const saveProgress = () => {
    // Save current progress without finalizing
    console.log('Progress saved');
  };

  const handleNext = () => {
    if (item && Array.isArray(item)) {
      const currentStep = item.find(i => i.type === 'activeStep')?.step || 0;
      const completedStages = item.find(i => i.type === 'completed_stage');

      if (completedStages && completedStages.level) {
        completedStages.level.push(currentStep);
        completedStages.level = [...new Set(completedStages.level)];
      }

      const updatedTask = task.map((taskItem, i) => {
        if (i === index) {
          return taskItem.map((ite) => {
            if (ite.type === 'status') {
              switch (currentStep) {
                case 0:
                  if (ite.value) ite.value.hazardsIdentification = 'completed';
                  break;
                case 1:
                  if (ite.value) ite.value.consequences = 'completed';
                  break;
                case 2:
                  if (ite.value) ite.value.currentControls = 'completed';
                  break;
                case 3:
                  if (ite.value) ite.value.riskEstimation = 'completed';
                  break;
                case 4:
                  if (ite.value) ite.value.additionalControls = 'completed';
                  break;
                default:
                  break;
              }
            }
            return ite;
          });
        }
        return taskItem;
      });

      setTask(updatedTask);
      setVisible(false);
    }
  };

  // Initialize data on component mount
  useEffect(() => {
    if (open) {
      getRoutineList();
      getHazardList();
      getAllResponsibility();
      getHighRiskHazardList();
    }
  }, [open]);

  return (
    <Dialog
      visible={open}
      onHide={() => onOpenChange(false)}
      style={{ width: '95vw', maxWidth: '1400px', height: '90vh' }}
      header="Add New Risk Assessment"
      modal
      className="p-fluid"
    >
      <div className="row" style={{ height: '100%', overflow: 'auto' }}>
        <div className="col-12">
          <div className="card">
            <div className='card-body p-0'>
              {/* Introduction Section */}
              <div className=' p-4 pt-0'>
                {type === 'routine' ? (
                  <>
                    <p className='fst-italic'>A risk evaluation conducted for activities that are regularly performed and require controls in place within the organization's operations.</p>
                    <p className=''>To proceed, select the relevant Department and Work Activity from the drop-down list of pre-configured options. If you cannot find a specific department or work activity, check the Risk Register, as a risk assessment may have already been completed for that activity. If it is not listed, please contact the Enterprise Administrator to have it added.</p>
                    <p className=''>If the work activity is not routinely conducted by the organization and is therefore not found in the work activity register, switch to Non-Routine Risk Assessment and manually enter the title of the work to continue.</p>
                  </>
                ) : (
                  <>
                    <p className='fst-italic '>An evaluation focused on activities that are not regularly performed and do not have established controls, requiring a detailed assessment to ensure appropriate safeguards are identified and implemented.</p>
                  </>
                )}
              </div>

              {/* Department and Activity Selection */}
              <div className='borderSection p-4'>
                {type === 'routine' ? (
                  <div className='row mb-4'>
                    <div className='col-4'>
                      <div className='mb-2'>Choose Operational Risk Area...</div>
                      <Select
                        labelKey="name"
                        id="user_description"
                        onChange={(e: any) => { setSelectedDepart(e); setSelectedActivity(null) }}
                        options={depart}
                        placeholder="Operational Risk Area"
                        value={selectedDepart}
                      />
                    </div>
                    <div className='col-8'>
                      <div className='mb-2'>Choose Work Activity...</div>
                      <Select
                        labelKey="name"
                        id="user_description"
                        onChange={(e: any) => setSelectedActivity(e)}
                        options={activity}
                        placeholder="WorkActivity"
                        value={selectedActivity}
                      />
                    </div>
                  </div>
                ) : type === 'nonroutine' ? (
                  <div className='row mb-4'>
                    <div className='col-12'>
                      <div className='mb-2'>To proceed, enter the title of the work to continue.</div>
                      <InputTextarea
                        autoResize
                        rows={3}
                        placeholder='Work Activity'
                        value={nonRoutineActivity}
                        onChange={(e) => setNonRoutineActivity(e.target.value)}
                        style={{ width: '100%' }}
                      />
                    </div>
                  </div>
                ) : (
                  <div className='row mb-4'>
                    <div className='col-8'>
                      <InputText
                        placeholder='Hazard Name'
                        value={hazardName}
                        onChange={(e) => setHazardName(e.target.value)}
                        style={{ width: '100%' }}
                      />
                    </div>
                  </div>
                )}

                {/* Team Members Selection */}
                <div className='row mb-4'>
                  <div className='col-12'>
                    <div className='mb-2'>Identify the qualified RA Team Members to include in this Risk Assessment using the drop-down selector; only qualified members will be listed. If a required member is not listed, please contact the Administrator to have them added. Once you've made your selections, click the Send Notification button to notify them via email about their inclusion in the team.</div>
                    <Select
                      labelKey="name"
                      valueKey="id"
                      id="user_description"
                      onChange={(e: any) => setSelectedCrew(e)}
                      options={crew}
                      isMulti={true}
                      value={selectedCrew}
                      getOptionLabel={(option: any) => option.name}
                      getOptionValue={(option: any) => option.id}
                      placeholder="Choose Members.."
                    />
                  </div>
                </div>
              </div>

              {/* Additional Information Section */}
              <div className='borderSection p-4'>
                <div className='row mb-4'>
                  <div className="d-flex flex-column col-12">
                    <label htmlFor="username" className='mb-2'>Provide additional information about the work activity to clarify scope (Optional)</label>
                    <InputTextarea
                      rows={3}
                      autoResize
                      cols={30}
                      value={activityDesc}
                      onChange={(e) => setActivityDesc(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              {/* Sub Activities Section */}
              <div className='borderSection p-4'>
                <div className='row mb-4'>
                  <div className='col-12'>
                    <h4 className="risk-title-sub mb-3">Sub Activities</h4>
                    <div className='mb-2'>Identify and list all sub-activities associated with the selected process. For each sub-activity, provide a clear description and, where feasible, upload any relevant images. These images will be utilized in the risk communication modules to enhance understanding and awareness of the process and associated risks.</div>
                    <div className='mb-2'>Click on each listed sub-activity and follow the provided guidance to identify the hazards, potential consequences, current controls, and assess the risks specific to that sub-activity.</div>
                  </div>
                </div>

                <div className='col-12 mb-4'>
                  {task.length === 0 ? (
                    <p>No sub-activities added</p>
                  ) : (
                    task.map((item, i) => {
                      const activityItem = item.find(ite => ite.type === 'activity');
                      const statusItem = item.find(ite => ite.type === 'status');

                      return (
                        <div key={i} className="card mb-3">
                          <div className="card-body">
                            <div className="d-flex justify-content-between align-items-center">
                              <h5 className="card-title">{activityItem?.name || 'Unnamed Activity'}</h5>
                              <div className="d-flex align-items-center">
                                <span className={`badge me-2 ${getStatusClass(item)}`}>
                                  {statusItem && statusItem.value ?
                                    Object.values(statusItem.value).some((status: any) => status === 'completed') ? 'Completed' :
                                    Object.values(statusItem.value).some((status: any) => status === 'inprogress') ? 'In Progress' : 'Not Started'
                                    : 'Not Started'
                                  }
                                </span>
                                <Button
                                  label="Edit"
                                  className="p-button-sm p-button-outlined me-2"
                                  onClick={() => openDialog(item, i)}
                                />
                                <Button
                                  label="Delete"
                                  className="p-button-sm p-button-danger"
                                  onClick={(e) => deleteTask(e, i)}
                                />
                              </div>
                            </div>
                            {activityItem?.images && activityItem.images.length > 0 && (
                              <div className="mt-2">
                                <small className="text-muted">{activityItem.images.length} image(s) attached</small>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>

                <div className='row'>
                  <div className='col-4'>
                    <Button
                      label="Add Sub-Activity"
                      outlined
                      className='d-flex'
                      onClick={() => setAddSubActivity(!addSubActivity)}
                    />
                  </div>
                </div>

                {addSubActivity && (
                  <div className='p-4 mt-4' style={{ border: '1px solid rgba(209, 213, 219, 1)' }}>
                    <div className='col-12'>
                      <div className="d-flex flex-column col-12">
                        <label htmlFor="username" className='mb-2'>Sub-Activity Name</label>
                        <InputText
                          value={subActivityName}
                          onChange={(e) => setSubActivityName(e.target.value)}
                        />
                      </div>
                    </div>
                    <div className='col-12 mt-3'>
                      <label htmlFor="username" className='mb-2'>Image Uploads</label>
                      <div className="mb-3">
                        <DropzoneArea
                          acceptedFiles={['image/jpeg', 'image/png']}
                          dropzoneText={"Drag 'n' drop some files here, or click to select files"}
                          filesLimit={5}
                          maxFileSize={104857600}
                          onChange={(files) => handleFileChange(files)}
                          showPreviewsInDropzone={false}
                          showPreviews={true}
                          dropzoneClass={'dropzoneText d-flex align-items-center justify-content-center '}
                        />
                      </div>
                    </div>
                    <div className='col-12 mt-3'>
                      <Button label="Save" onClick={() => AddSubActivityTitle()} />
                    </div>
                  </div>
                )}
              </div>

              {/* Overall Recommendations Section */}
              <div className='borderSection p-4'>
                <h5 className="mb-4 fw-bold">Overall recommendations of the RA Team</h5>
                <div className='row mb-4'>
                  <div className='col-12'>
                    <div className='row'>
                      <div className='col-12 mb-3 d-flex align-items-start'>
                        <div className="number-circle me-3">1</div>
                        <div className='flex-grow-1'>
                          <Select
                            labelKey="label"
                            id="user_description"
                            value={recommendationOne}
                            onChange={(e: any) => setRecommendationOne(e)}
                            options={[
                              { label: 'The overall risk level for this work activity is LOW. No further additional controls actions are necessary. Monitoring is required to ensure that the controls are maintained.', value: '0' },
                              { label: 'The overall risk level for this work activity is MEDIUM. Work can progress with close supervision and monitoring of current controls. Additional controls identified should be implemented within the defined period of time.', value: '1' },
                              { label: 'The overall risk level for this work activity is HIGH. Work should not be started or continued until the risk level has been reduced and risk numbers enters the LOW or MEDIUM zone.', value: '2' }
                            ]}
                            placeholder="Choose ..."
                          />
                        </div>
                      </div>

                      <div className='col-12 mb-3 d-flex align-items-start'>
                        <div className="number-circle me-3">2</div>
                        <div className='flex-grow-1'>
                          <Select
                            labelKey="label"
                            id="user_description"
                            value={recommendationTwo}
                            onChange={(e: any) => setRecommendationTwo(e)}
                            options={[
                              { label: 'Since this is routine activity with low risk, no formal permit is recommended. However, a dynamic review is advised in case of changing circumstances.', value: '0' },
                              { label: 'A formal permit to work is required before work can commence. Duration will be specified by the permit approver.', value: '1' },
                              { label: 'A formal permit to work that is required. This is valid for a specific task and limited duration. The permit shall include the names of individuals involved in the work.', value: '2' }
                            ]}
                            placeholder="Choose ..."
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className='row mb-4'>
                  <div className='col-12 d-flex align-items-start'>
                    <div className="number-circle me-3">3</div>
                    <div className='flex-grow-1'>
                      <div className='col-12' style={{ border: '1px solid #ced4da' }}>
                        <p className='p-2'>Considering the hazards and risks associated with this work activity, the RA team requires the following high-risk permits to be approved and active when applying for a permit for this specific activity:</p>
                        <div className="d-flex col-12 p-3">
                          {risk.length !== 0 &&
                            risk.map((item, index) => (
                              <label key={index} className='label-role checkbox-bootstrap checkbox-lg col-4 me-3'>
                                <input
                                  value={item.hazardName}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setEptwHot((prev) => [...prev, item]);
                                    } else {
                                      setEptwHot(prevData => prevData.filter(item1 => item1.id !== item.id));
                                    }
                                  }}
                                  checked={eptwHot && eptwHot.some(item1 => item1.id === item.id)}
                                  type='checkbox'
                                  className='me-1'
                                />
                                <span className="checkbox-placeholder"></span>
                                {item.hazardName}
                              </label>
                            ))
                          }
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className='row mb-4'>
                  <div className='col-12 d-flex align-items-start'>
                    <div className="number-circle me-3">4</div>
                    <div className='flex-grow-1'>
                      <div className="d-flex flex-column col-12">
                        <label htmlFor="username" className='mb-2'>Additional Recommendation</label>
                        <InputTextarea
                          autoResize
                          rows={3}
                          cols={30}
                          value={additionalRecommendation}
                          onChange={(e) => setAdditionalRecommendation(e.target.value)}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Declaration Section */}
              <div className='borderSection p-4'>
                <h5 className="mb-4 fw-bold">Declaration</h5>
                <div className='row mb-2'>
                  {type === 'routine' ? (
                    <p>As the Team Leader for this Routine Risk Assessment, I affirm my role in guiding the identification of potential risks and necessary controls. The controls listed have been evaluated by the team based on their professional expertise and are essential for ensuring safety in this activity. This outcome reflects the collective judgment of the team, reached through collaboration and consensus.</p>
                  ) : type === 'nonroutine' ? (
                    <p>As the Team Leader for this Non-Routine Risk Assessment, I confirm my responsibility in guiding the team through the identification of potential risks and necessary controls for activities that are not part of the organization's routine work activity inventory. Given that these activities are not regularly undertaken, we are placing additional focus on documenting the risks and ensuring that appropriate controls are in place. The controls identified are essential for maintaining safety, and this conclusion has been reached through consensus based on the team's collective professional judgment and experience.</p>
                  ) : (
                    <p>As the Team Leader for this High-Risk Hazard Assessment, I confirm my responsibility in guiding the team through the identification of potential risks and necessary controls for high-risk activities. The controls identified are essential for maintaining safety, and this conclusion has been reached through consensus based on the team's collective professional judgment and experience.</p>
                  )}
                </div>

                <div className='row mb-4 text-center'>
                  <div className="d-flex flex-column col-12">
                    <div className="row mt-4">
                      <div className="col-12">
                        <SignatureCanvas
                          penColor="#1F3BB3"
                          canvasProps={{
                            width: 450,
                            height: 120,
                            className: "sigCanvas",
                            style: {
                              boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                            },
                          }}
                          ref={signRef}
                        />
                        <i className="fa fa-undo undo" onClick={() => signRef.current?.clear()}></i>
                        <p>{user?.firstName || 'Team Leader'}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="col-12 text-center" style={{ padding: 20 }}>
                <button
                  style={{ marginRight: 10 }}
                  type="button"
                  className="btn btn-primary mb-3"
                  onClick={(e) => {
                    e.preventDefault();
                    // Handle draft save
                    onSubmit({
                      type,
                      task,
                      selectedDepart,
                      selectedActivity,
                      selectedCrew,
                      activityDesc,
                      recommendationOne,
                      recommendationTwo,
                      additionalRecommendation,
                      eptwHot,
                      nonRoutineDepartment,
                      nonRoutineActivity,
                      hazardName,
                      shortName
                    }, true);
                  }}
                >
                  Save as Draft
                </button>
                <button
                  type="button"
                  className="btn btn-secondary mb-3"
                  onClick={(e) => {
                    e.preventDefault();
                    // Handle final submission
                    onSubmit({
                      type,
                      task,
                      selectedDepart,
                      selectedActivity,
                      selectedCrew,
                      activityDesc,
                      recommendationOne,
                      recommendationTwo,
                      additionalRecommendation,
                      eptwHot,
                      nonRoutineDepartment,
                      nonRoutineActivity,
                      hazardName,
                      shortName
                    }, false);
                  }}
                >
                  Release Draft for Affirmation
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Sub-Activity Modal */}
      {item !== '' && Array.isArray(item) && (
        <Modal show={visible} onHide={() => setVisible(false)} size='lg'>
          <Modal.Header closeButton>
            <Modal.Title>Sub-activity Risk Assessment</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <SubActivityComponent
              item={item}
              onSave={editSubActivityTitle}
            />

            <hr />
            {(() => {
              const activeStepItem = item.find(i => i.type === 'activeStep');
              const stageItem = item.find(i => i.type === 'stage');
              const statusItem = item.find(i => i.type === 'status');

              return activeStepItem && stageItem && (
                <HeadStepper
                  activeStage={activeStepItem.step || 0}
                  stages={stageItem.level || []}
                  stageStatus={statusItem?.value || {}}
                  handleStageClick={handleStageClick}
                  getStatusClass={getStatusClass}
                />
              );
            })()}
            <hr />

            {(() => {
              const activeStepItem = item.find(i => i.type === 'activeStep');
              const currentStep = activeStepItem?.step || 0;

              switch (currentStep) {
                case 0:
                  return (
                    <div>
                      <p>For this sub-activity, identify associated hazards by selecting from the various hazard types displayed. Each type will feature icons representing specific hazards. Once selected, these hazards will automatically appear in other modules, such as Risk Communication, Permit to Work, and Toolbox Talks</p>
                      <p>If you encounter a specific hazard that is not included in the library, please send an <NAME_EMAIL> so that it can be promptly added to the hazard library.</p>

                      {/* Hazards Identification Panel */}
                      <div className="w-100">
                        <TabView activeIndex={activeTabIndex} onTabChange={(e) => setActiveTabIndex(e.index)}>
                          {hazards.map((hazard: any, index: number) => (
                            <TabPanel key={index} header={hazard.name}>
                              <div className="row">
                                {hazard.hazardItems?.map((hazardItem: any, j: number) => {
                                  const hazardData = item.find(i => i.type === 'hazards');
                                  const isSelected = hazardData?.selected?.some((h: any) => h.id === hazardItem.id);

                                  return (
                                    <div key={j} className="col-4 mb-3">
                                      <div
                                        className={`p-3 text-center border rounded cursor-pointer ${
                                          isSelected ? 'bg-primary text-white' : 'bg-light'
                                        }`}
                                        onClick={() => onClickHazards(hazardItem, j)}
                                        style={{ cursor: 'pointer' }}
                                      >
                                        <p className="mb-0">{hazardItem.name}</p>
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            </TabPanel>
                          ))}
                        </TabView>
                      </div>

                      {/* Identified Hazards */}
                      {(() => {
                        const hazardData = item.find(i => i.type === 'hazards');
                        const selectedHazards = hazardData?.selected || [];

                        return selectedHazards.length > 0 && (
                          <>
                            <h6 className='mt-4 mb-3'>Hazards Identified</h6>
                            <div className='row'>
                              {selectedHazards.map((hazardItem: any, index: number) => (
                                <div className='col-3 mb-3' key={index}>
                                  <div className='d-flex justify-content-between align-items-center p-2' style={{ border: '1px solid #E5E7EB', borderRadius: 8 }}>
                                    <p className="mb-0">{hazardItem.name}</p>
                                    <i className='pi pi-times' onClick={() => onDeleteHaz(hazardItem)} style={{ cursor: 'pointer' }}></i>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </>
                        );
                      })()}
                    </div>
                  );

                case 1:
                  return (
                    <div>
                      <h6 className='fw-bold'>Consequences</h6>
                      <p>For this sub-activity, list and elaborate on all potential consequences associated with the identified hazards, covering impacts on Personnel, Environment, Property/Equipment, and Operations, as applicable. Include all relevant areas that apply to the specific hazards of the sub-activity.</p>
                      <div className="p-4 border rounded">
                        <p className="text-muted">Consequences Panel - Component to be implemented</p>
                      </div>
                    </div>
                  );

                case 2:
                  return (
                    <div>
                      <h6 className='fw-bold'>Current Controls</h6>
                      <p>Identify and describe in detail the current controls in place to manage the hazards and minimize their consequences. Current controls refer to existing safety measures, procedures, and other implemented actions to reduce risks associated with the sub-activity.</p>
                      <div className="p-4 border rounded">
                        <p className="text-muted">Current Controls Panel - Component to be implemented</p>
                      </div>
                    </div>
                  );

                case 3:
                  return (
                    <div>
                      <h6 className='fw-bold'>Risk Assessment</h6>
                      <p>Assess the risk level by evaluating the likelihood and severity of potential consequences.</p>
                      <div className="p-4 border rounded">
                        <p className="text-muted">Risk Assessment Panel - Component to be implemented</p>
                      </div>
                    </div>
                  );

                case 4:
                  return (
                    <div>
                      <h6 className='fw-bold'>Additional Controls</h6>
                      <p>Identify additional risk management measures that should be implemented to further reduce risks.</p>
                      <div className="p-4 border rounded">
                        <p className="text-muted">Additional Controls Panel - Component to be implemented</p>
                      </div>
                    </div>
                  );

                default:
                  return null;
              }
            })()}
          </Modal.Body>

          <Modal.Footer>
            <div className="d-flex justify-content-between align-items-center w-100">
              <div className="d-flex">
                <Button
                  className='me-2'
                  outlined
                  label={`Save Progress`}
                  onClick={saveProgress}
                />
                <Button
                  label={`Save & Finalize ${(() => {
                    const activeStepItem = item.find(i => i.type === 'activeStep');
                    const currentStep = activeStepItem?.step || 0;
                    return sectionNames[currentStep] || 'Section';
                  })()} for Sub Activity`}
                  onClick={handleNext}
                />
              </div>
            </div>
          </Modal.Footer>
        </Modal>
      )}
    </Dialog>
  );
};

export default AddRiskAssessmentModal;

// Add required CSS styles
const styles = `
.borderSection {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.risk-title-sub {
  color: #1f2937;
  font-weight: 600;
}

.number-circle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.dropzoneText {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  background-color: #f9fafb;
}

.boxShadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  padding: 0.5rem;
}

.pointer {
  cursor: pointer;
}

.borderRed {
  border-color: #ef4444 !important;
}

.cell-green {
  background-color: #dcfce7;
  color: #166534;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.cell-yellow {
  background-color: #fef3c7;
  color: #92400e;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.cell-red {
  background-color: #fecaca;
  color: #991b1b;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.level-1 { background-color: #f0f9ff; }
.level-2 { background-color: #ecfdf5; }
.level-3 { background-color: #fffbeb; }
.level-4 { background-color: #fef2f2; }
.level-5 { background-color: #fdf2f8; }

.stepper-container {
  margin: 1rem 0;
}

.label-role {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.checkbox-bootstrap {
  position: relative;
}

.checkbox-placeholder {
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  margin-right: 8px;
  display: inline-block;
  position: relative;
}

.checkbox-bootstrap input[type="checkbox"]:checked + .checkbox-placeholder {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.checkbox-bootstrap input[type="checkbox"]:checked + .checkbox-placeholder::after {
  content: "✓";
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.sigCanvas {
  border: 2px solid #d1d5db;
  border-radius: 8px;
}

.undo {
  position: absolute;
  top: 5px;
  right: 5px;
  cursor: pointer;
  background: #ef4444;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}
