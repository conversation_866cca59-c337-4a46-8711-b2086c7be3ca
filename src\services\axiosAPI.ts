import axios from 'axios';
import { API_BASE_URL } from '../constants/index';
import { store } from '../store';
import { setLogout } from '../store/slices/authSlice';

// Create an axios instance with default configuration
const API = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include the auth token in requests
API.interceptors.request.use(
  (config) => {
    // Get the token from localStorage (using the correct key)
    const token = localStorage.getItem('access_token');

    // If token exists, add it to the headers
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle common errors
API.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle 401 Unauthorized errors (token expired)
    if (error.response && error.response.status === 401) {
      console.log('401 Unauthorized - Logging out user');

      try {
        // Dispatch logout action to update Redux state
        store.dispatch(setLogout());

        // Clear all auth-related data from localStorage
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('headerLogoUrl'); // Clear cached logo

        // Only redirect if we're not already on the login page
        if (!window.location.pathname.includes('/login')) {
          // Use replace to prevent back button issues
          window.location.replace('/login');
        }
      } catch (logoutError) {
        console.error('Error during logout process:', logoutError);
      }
    }

    return Promise.reject(error);
  }
);

export default API;
